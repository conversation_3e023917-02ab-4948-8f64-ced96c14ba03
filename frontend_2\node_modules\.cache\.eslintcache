[{"D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\index.js": "1", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\reportWebVitals.js": "2", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\App.js": "3", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\route.jsx": "4", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\NoUserSelected.jsx": "5", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\Chat.jsx": "6", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\ProtectedRoute.jsx": "7", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\register.jsx": "8", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\login.jsx": "9", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\index.jsx": "10", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\state.js": "11", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\functions.js": "12", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatHeader.jsx": "13", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatFooter.jsx": "14", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatMessage.jsx": "15", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\requests.js": "16", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Sidebar\\Sidebar.jsx": "17", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Sidebar\\MessageItem.jsx": "18", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Profile\\Profile.jsx": "19", "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Profile\\Editablenput.jsx": "20"}, {"size": 576, "mtime": 1743196655701, "results": "21", "hashOfConfig": "22"}, {"size": 362, "mtime": 1734771301764, "results": "23", "hashOfConfig": "22"}, {"size": 229, "mtime": 1743196741532, "results": "24", "hashOfConfig": "22"}, {"size": 2003, "mtime": 1743196504204, "results": "25", "hashOfConfig": "22"}, {"size": 1408, "mtime": 1737145003264, "results": "26", "hashOfConfig": "22"}, {"size": 1306, "mtime": 1743764375319, "results": "27", "hashOfConfig": "22"}, {"size": 532, "mtime": 1743198345038, "results": "28", "hashOfConfig": "22"}, {"size": 4507, "mtime": 1743197135404, "results": "29", "hashOfConfig": "22"}, {"size": 3192, "mtime": 1743709914931, "results": "30", "hashOfConfig": "22"}, {"size": 3401, "mtime": 1748251992678, "results": "31", "hashOfConfig": "22"}, {"size": 1512, "mtime": 1743602832189, "results": "32", "hashOfConfig": "22"}, {"size": 175, "mtime": 1743713276204, "results": "33", "hashOfConfig": "22"}, {"size": 1752, "mtime": 1743761244246, "results": "34", "hashOfConfig": "22"}, {"size": 2236, "mtime": 1743760924400, "results": "35", "hashOfConfig": "22"}, {"size": 826, "mtime": 1735985035526, "results": "36", "hashOfConfig": "22"}, {"size": 2052, "mtime": 1737144548093, "results": "37", "hashOfConfig": "22"}, {"size": 3427, "mtime": 1748252049861, "results": "38", "hashOfConfig": "22"}, {"size": 2778, "mtime": 1748252042217, "results": "39", "hashOfConfig": "22"}, {"size": 4519, "mtime": 1743766050623, "results": "40", "hashOfConfig": "22"}, {"size": 1595, "mtime": 1743713970255, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1z05e25", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\index.js", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\reportWebVitals.js", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\App.js", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\route.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\NoUserSelected.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\Chat.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\register.jsx", ["102"], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\login.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\pages\\index.jsx", ["103", "104", "105", "106", "107", "108", "109", "110", "111", "112", "113", "114", "115"], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\state.js", ["116"], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\functions.js", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatHeader.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatFooter.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Chat\\ChatMessage.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\libs\\requests.js", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Sidebar\\Sidebar.jsx", [], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Sidebar\\MessageItem.jsx", ["117"], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Profile\\Profile.jsx", ["118", "119"], [], "D:\\my Programing\\whatsapp-clone-2\\frontend_2\\src\\components\\Profile\\Editablenput.jsx", [], [], {"ruleId": "120", "severity": 1, "message": "121", "line": 12, "column": 22, "nodeType": "122", "messageId": "123", "endLine": 12, "endColumn": 33}, {"ruleId": "120", "severity": 1, "message": "124", "line": 2, "column": 21, "nodeType": "122", "messageId": "123", "endLine": 2, "endColumn": 32}, {"ruleId": "120", "severity": 1, "message": "125", "line": 6, "column": 10, "nodeType": "122", "messageId": "123", "endLine": 6, "endColumn": 21}, {"ruleId": "120", "severity": 1, "message": "126", "line": 6, "column": 23, "nodeType": "122", "messageId": "123", "endLine": 6, "endColumn": 31}, {"ruleId": "120", "severity": 1, "message": "127", "line": 11, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 11, "endColumn": 15}, {"ruleId": "120", "severity": 1, "message": "128", "line": 15, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 15, "endColumn": 12}, {"ruleId": "120", "severity": 1, "message": "129", "line": 16, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 16, "endColumn": 17}, {"ruleId": "120", "severity": 1, "message": "130", "line": 18, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 18, "endColumn": 14}, {"ruleId": "120", "severity": 1, "message": "131", "line": 19, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 19, "endColumn": 23}, {"ruleId": "120", "severity": 1, "message": "132", "line": 20, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 20, "endColumn": 9}, {"ruleId": "120", "severity": 1, "message": "133", "line": 22, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 22, "endColumn": 20}, {"ruleId": "120", "severity": 1, "message": "134", "line": 24, "column": 5, "nodeType": "122", "messageId": "123", "endLine": 24, "endColumn": 12}, {"ruleId": "135", "severity": 1, "message": "136", "line": 108, "column": 6, "nodeType": "137", "endLine": 108, "endColumn": 30, "suggestions": "138"}, {"ruleId": "135", "severity": 1, "message": "139", "line": 116, "column": 6, "nodeType": "137", "endLine": 116, "endColumn": 19, "suggestions": "140"}, {"ruleId": "120", "severity": 1, "message": "133", "line": 5, "column": 7, "nodeType": "122", "messageId": "123", "endLine": 5, "endColumn": 22}, {"ruleId": "120", "severity": 1, "message": "133", "line": 13, "column": 11, "nodeType": "122", "messageId": "123", "endLine": 13, "endColumn": 26}, {"ruleId": "120", "severity": 1, "message": "141", "line": 10, "column": 12, "nodeType": "122", "messageId": "123", "endLine": 10, "endColumn": 16}, {"ruleId": "120", "severity": 1, "message": "142", "line": 52, "column": 11, "nodeType": "122", "messageId": "123", "endLine": 52, "endColumn": 29}, "no-unused-vars", "'accessToken' is assigned a value but never used.", "Identifier", "unusedVar", "'useCallback' is defined but never used.", "'getMessages' is defined but never used.", "'getUsers' is defined but never used.", "'addMessage' is assigned a value but never used.", "'setUser' is assigned a value but never used.", "'updateFriend' is assigned a value but never used.", "'addFriend' is assigned a value but never used.", "'setCurrentReceiver' is assigned a value but never used.", "'user' is assigned a value but never used.", "'currentReceiver' is assigned a value but never used.", "'friends' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'setTyping'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["144"], "'name' is assigned a value but never used.", "'toggleActiveStatus' is assigned a value but never used.", {"desc": "145", "fix": "146"}, {"desc": "147", "fix": "148"}, "Update the dependencies array to be: [accessToken, setSocket, setTyping]", {"range": "149", "text": "150"}, "Update the dependencies array to be: [accessToken, fetchData]", {"range": "151", "text": "152"}, [2961, 2985], "[accessToken, setSocket, setTyping]", [3109, 3122], "[accessToken, fetchData]"]