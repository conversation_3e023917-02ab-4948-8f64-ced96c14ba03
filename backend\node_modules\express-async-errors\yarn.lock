# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


accepts@~1.3.4:
  version "1.3.4"
  resolved "https://registry.yarnpkg.com/accepts/-/accepts-1.3.4.tgz#86246758c7dd6d21a6474ff084a4740ec05eb21f"
  dependencies:
    mime-types "~2.1.16"
    negotiator "0.6.1"

acorn-jsx@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/acorn-jsx/-/acorn-jsx-3.0.1.tgz#afdf9488fb1ecefc8348f6fb22f464e32a58b36b"
  dependencies:
    acorn "^3.0.4"

acorn@^3.0.4:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-3.3.0.tgz#45e37fb39e8da3f25baee3ff5369e2bb5f22017a"

acorn@^5.1.1:
  version "5.2.1"
  resolved "https://registry.yarnpkg.com/acorn/-/acorn-5.2.1.tgz#317ac7821826c22c702d66189ab8359675f135d7"

ajv-keywords@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ajv-keywords/-/ajv-keywords-2.1.1.tgz#617997fc5f60576894c435f940d819e135b80762"

ajv@^5.2.0, ajv@^5.2.3:
  version "5.3.0"
  resolved "https://registry.yarnpkg.com/ajv/-/ajv-5.3.0.tgz#4414ff74a50879c208ee5fdc826e32c303549eda"
  dependencies:
    co "^4.6.0"
    fast-deep-equal "^1.0.0"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.3.0"

ansi-escapes@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/ansi-escapes/-/ansi-escapes-3.0.0.tgz#ec3e8b4e9f8064fc02c3ac9b65f1c275bda8ef92"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"

ansi-regex@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/ansi-regex/-/ansi-regex-3.0.0.tgz#ed0317c322064f79466c02966bddb605ab37d998"

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"

ansi-styles@^3.1.0:
  version "3.2.0"
  resolved "https://registry.yarnpkg.com/ansi-styles/-/ansi-styles-3.2.0.tgz#c159b8d5be0f9e5a6f346dab94f16ce022161b88"
  dependencies:
    color-convert "^1.9.0"

argparse@^1.0.7:
  version "1.0.9"
  resolved "https://registry.yarnpkg.com/argparse/-/argparse-1.0.9.tgz#73d83bc263f86e97f8cc4f6bae1b0e90a7d22c86"
  dependencies:
    sprintf-js "~1.0.2"

array-flatten@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/array-flatten/-/array-flatten-1.1.1.tgz#9a5f699051b1e7073328f2a008968b64ea2955d2"

array-union@^1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/array-union/-/array-union-1.0.2.tgz#9a34410e4f4e3da23dea375be5be70f24778ec39"
  dependencies:
    array-uniq "^1.0.1"

array-uniq@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/array-uniq/-/array-uniq-1.0.3.tgz#af6ac877a25cc7f74e058894753858dfdb24fdb6"

arrify@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/arrify/-/arrify-1.0.1.tgz#898508da2226f380df904728456849c1501a4b0d"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "https://registry.yarnpkg.com/asynckit/-/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"

babel-code-frame@^6.22.0:
  version "6.26.0"
  resolved "https://registry.yarnpkg.com/babel-code-frame/-/babel-code-frame-6.26.0.tgz#63fd43f7dc1e3bb7ce35947db8fe369a3f58c74b"
  dependencies:
    chalk "^1.1.3"
    esutils "^2.0.2"
    js-tokens "^3.0.2"

balanced-match@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/balanced-match/-/balanced-match-1.0.0.tgz#89b4d199ab2bee49de164ea02b89ce462d71b767"

body-parser@1.18.2:
  version "1.18.2"
  resolved "https://registry.yarnpkg.com/body-parser/-/body-parser-1.18.2.tgz#87678a19d84b47d859b83199bd59bce222b10454"
  dependencies:
    bytes "3.0.0"
    content-type "~1.0.4"
    debug "2.6.9"
    depd "~1.1.1"
    http-errors "~1.6.2"
    iconv-lite "0.4.19"
    on-finished "~2.3.0"
    qs "6.5.1"
    raw-body "2.3.2"
    type-is "~1.6.15"

brace-expansion@^1.1.7:
  version "1.1.8"
  resolved "https://registry.yarnpkg.com/brace-expansion/-/brace-expansion-1.1.8.tgz#c07b211c7c952ec1f8efd51a77ef0d1d3990a292"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

browser-stdout@1.3.0:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/browser-stdout/-/browser-stdout-1.3.0.tgz#f351d32969d32fa5d7a5567154263d928ae3bd1f"

builtin-modules@^1.0.0, builtin-modules@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/builtin-modules/-/builtin-modules-1.1.1.tgz#270f076c5a72c02f5b65a47df94c5fe3a278892f"

bytes@3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/bytes/-/bytes-3.0.0.tgz#d32815404d689699f85a4ea4fa8755dd13a96048"

caller-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/caller-path/-/caller-path-0.1.0.tgz#94085ef63581ecd3daa92444a8fe94e82577751f"
  dependencies:
    callsites "^0.2.0"

callsites@^0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/callsites/-/callsites-0.2.0.tgz#afab96262910a7f33c19a5775825c69f34e350ca"

chalk@^1.1.3:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.0.0, chalk@^2.1.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/chalk/-/chalk-2.3.0.tgz#b5ea48efc9c1793dccc9b4767c93914d3f2d52ba"
  dependencies:
    ansi-styles "^3.1.0"
    escape-string-regexp "^1.0.5"
    supports-color "^4.0.0"

ci-info@^1.0.0:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/ci-info/-/ci-info-1.1.1.tgz#47b44df118c48d2597b56d342e7e25791060171a"

circular-json@^0.3.1:
  version "0.3.3"
  resolved "https://registry.yarnpkg.com/circular-json/-/circular-json-0.3.3.tgz#815c99ea84f6809529d2f45791bdf82711352d66"

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/cli-cursor/-/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  dependencies:
    restore-cursor "^2.0.0"

cli-width@^2.0.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/cli-width/-/cli-width-2.2.0.tgz#ff19ede8a9a5e579324147b0c11f0fbcbabed639"

co@^4.6.0:
  version "4.6.0"
  resolved "https://registry.yarnpkg.com/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"

color-convert@^1.9.0:
  version "1.9.0"
  resolved "https://registry.yarnpkg.com/color-convert/-/color-convert-1.9.0.tgz#1accf97dd739b983bf994d56fec8f95853641b7a"
  dependencies:
    color-name "^1.1.1"

color-name@^1.1.1:
  version "1.1.3"
  resolved "https://registry.yarnpkg.com/color-name/-/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"

combined-stream@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/combined-stream/-/combined-stream-1.0.5.tgz#938370a57b4a51dea2c77c15d5c5fdf895164009"
  dependencies:
    delayed-stream "~1.0.0"

commander@2.11.0:
  version "2.11.0"
  resolved "https://registry.yarnpkg.com/commander/-/commander-2.11.0.tgz#157152fd1e7a6c8d98a5b715cf376df928004563"

component-emitter@^1.2.0:
  version "1.2.1"
  resolved "https://registry.yarnpkg.com/component-emitter/-/component-emitter-1.2.1.tgz#137918d6d78283f7df7a6b7c5a63e140e69425e6"

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://registry.yarnpkg.com/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"

concat-stream@^1.6.0:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/concat-stream/-/concat-stream-1.6.0.tgz#0aac662fd52be78964d5532f694784e70110acf7"
  dependencies:
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

contains-path@^0.1.0:
  version "0.1.0"
  resolved "https://registry.yarnpkg.com/contains-path/-/contains-path-0.1.0.tgz#fe8cf184ff6670b6baef01a9d4861a5cbec4120a"

content-disposition@0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/content-disposition/-/content-disposition-0.5.2.tgz#0cf68bb9ddf5f2be7961c3a85178cb85dba78cb4"

content-type@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/content-type/-/content-type-1.0.4.tgz#e138cc75e040c727b1966fe5e5f8c9aee256fe3b"

cookie-signature@1.0.6:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/cookie-signature/-/cookie-signature-1.0.6.tgz#e303a882b342cc3ee8ca513a79999734dab3ae2c"

cookie@0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/cookie/-/cookie-0.3.1.tgz#e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb"

cookiejar@^2.1.0:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/cookiejar/-/cookiejar-2.1.1.tgz#41ad57b1b555951ec171412a81942b1e8200d34a"

core-util-is@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/core-util-is/-/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"

cross-spawn@^5.1.0:
  version "5.1.0"
  resolved "https://registry.yarnpkg.com/cross-spawn/-/cross-spawn-5.1.0.tgz#e8bd0efee58fcff6f8f94510a0a554bbfa235449"
  dependencies:
    lru-cache "^4.0.1"
    shebang-command "^1.2.0"
    which "^1.2.9"

debug@2.6.9, debug@^2.6.8:
  version "2.6.9"
  resolved "https://registry.yarnpkg.com/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  dependencies:
    ms "2.0.0"

debug@3.1.0, debug@^3.0.1, debug@^3.1.0:
  version "3.1.0"
  resolved "https://registry.yarnpkg.com/debug/-/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  dependencies:
    ms "2.0.0"

deep-is@~0.1.3:
  version "0.1.3"
  resolved "https://registry.yarnpkg.com/deep-is/-/deep-is-0.1.3.tgz#b369d6fb5dbc13eecf524f91b070feedc357cf34"

del@^2.0.2:
  version "2.2.2"
  resolved "https://registry.yarnpkg.com/del/-/del-2.2.2.tgz#c12c981d067846c84bcaf862cff930d907ffd1a8"
  dependencies:
    globby "^5.0.0"
    is-path-cwd "^1.0.0"
    is-path-in-cwd "^1.0.0"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"
    rimraf "^2.2.8"

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/delayed-stream/-/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"

depd@1.1.1, depd@~1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/depd/-/depd-1.1.1.tgz#5783b4e1c459f06fa5ca27f991f3d06e7a310359"

destroy@~1.0.4:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/destroy/-/destroy-1.0.4.tgz#978857442c44749e4206613e37946205826abd80"

diff@3.3.1:
  version "3.3.1"
  resolved "https://registry.yarnpkg.com/diff/-/diff-3.3.1.tgz#aa8567a6eed03c531fc89d3f711cd0e5259dec75"

doctrine@1.5.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-1.5.0.tgz#379dce730f6166f76cefa4e6707a159b02c5a6fa"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

doctrine@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/doctrine/-/doctrine-2.0.0.tgz#c73d8d2909d22291e1a007a395804da8b665fe63"
  dependencies:
    esutils "^2.0.2"
    isarray "^1.0.0"

ee-first@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/ee-first/-/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"

encodeurl@~1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/encodeurl/-/encodeurl-1.0.1.tgz#79e3d58655346909fe6f0f45a5de68103b294d20"

error-ex@^1.2.0:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/error-ex/-/error-ex-1.3.1.tgz#f855a86ce61adc4e8621c3cda21e7a7612c3a8dc"
  dependencies:
    is-arrayish "^0.2.1"

escape-html@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/escape-html/-/escape-html-1.0.3.tgz#0258eae4d3d0c0974de1c169188ef0051d1d1988"

escape-string-regexp@1.0.5, escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"

eslint-config-airbnb-base@^12.1.0:
  version "12.1.0"
  resolved "https://registry.yarnpkg.com/eslint-config-airbnb-base/-/eslint-config-airbnb-base-12.1.0.tgz#386441e54a12ccd957b0a92564a4bafebd747944"
  dependencies:
    eslint-restricted-globals "^0.1.1"

eslint-import-resolver-node@^0.3.1:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.1.tgz#4422574cde66a9a7b099938ee4d508a199e0e3cc"
  dependencies:
    debug "^2.6.8"
    resolve "^1.2.0"

eslint-module-utils@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/eslint-module-utils/-/eslint-module-utils-2.1.1.tgz#abaec824177613b8a95b299639e1b6facf473449"
  dependencies:
    debug "^2.6.8"
    pkg-dir "^1.0.0"

eslint-plugin-import@^2.8.0:
  version "2.8.0"
  resolved "https://registry.yarnpkg.com/eslint-plugin-import/-/eslint-plugin-import-2.8.0.tgz#fa1b6ef31fcb3c501c09859c1b86f1fc5b986894"
  dependencies:
    builtin-modules "^1.1.1"
    contains-path "^0.1.0"
    debug "^2.6.8"
    doctrine "1.5.0"
    eslint-import-resolver-node "^0.3.1"
    eslint-module-utils "^2.1.1"
    has "^1.0.1"
    lodash.cond "^4.3.0"
    minimatch "^3.0.3"
    read-pkg-up "^2.0.0"

eslint-restricted-globals@^0.1.1:
  version "0.1.1"
  resolved "https://registry.yarnpkg.com/eslint-restricted-globals/-/eslint-restricted-globals-0.1.1.tgz#35f0d5cbc64c2e3ed62e93b4b1a7af05ba7ed4d7"

eslint-scope@^3.7.1:
  version "3.7.1"
  resolved "https://registry.yarnpkg.com/eslint-scope/-/eslint-scope-3.7.1.tgz#3d63c3edfda02e06e01a452ad88caacc7cdcb6e8"
  dependencies:
    esrecurse "^4.1.0"
    estraverse "^4.1.1"

eslint@^4.10.0:
  version "4.10.0"
  resolved "https://registry.yarnpkg.com/eslint/-/eslint-4.10.0.tgz#f25d0d7955c81968c2309aa5c9a229e045176bb7"
  dependencies:
    ajv "^5.2.0"
    babel-code-frame "^6.22.0"
    chalk "^2.1.0"
    concat-stream "^1.6.0"
    cross-spawn "^5.1.0"
    debug "^3.0.1"
    doctrine "^2.0.0"
    eslint-scope "^3.7.1"
    espree "^3.5.1"
    esquery "^1.0.0"
    estraverse "^4.2.0"
    esutils "^2.0.2"
    file-entry-cache "^2.0.0"
    functional-red-black-tree "^1.0.1"
    glob "^7.1.2"
    globals "^9.17.0"
    ignore "^3.3.3"
    imurmurhash "^0.1.4"
    inquirer "^3.0.6"
    is-resolvable "^1.0.0"
    js-yaml "^3.9.1"
    json-stable-stringify "^1.0.1"
    levn "^0.3.0"
    lodash "^4.17.4"
    minimatch "^3.0.2"
    mkdirp "^0.5.1"
    natural-compare "^1.4.0"
    optionator "^0.8.2"
    path-is-inside "^1.0.2"
    pluralize "^7.0.0"
    progress "^2.0.0"
    require-uncached "^1.0.3"
    semver "^5.3.0"
    strip-ansi "^4.0.0"
    strip-json-comments "~2.0.1"
    table "^4.0.1"
    text-table "~0.2.0"

espree@^3.5.1:
  version "3.5.1"
  resolved "https://registry.yarnpkg.com/espree/-/espree-3.5.1.tgz#0c988b8ab46db53100a1954ae4ba995ddd27d87e"
  dependencies:
    acorn "^5.1.1"
    acorn-jsx "^3.0.0"

esprima@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/esprima/-/esprima-4.0.0.tgz#4499eddcd1110e0b218bacf2fa7f7f59f55ca804"

esquery@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/esquery/-/esquery-1.0.0.tgz#cfba8b57d7fba93f17298a8a006a04cda13d80fa"
  dependencies:
    estraverse "^4.0.0"

esrecurse@^4.1.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/esrecurse/-/esrecurse-4.2.0.tgz#fa9568d98d3823f9a41d91e902dcab9ea6e5b163"
  dependencies:
    estraverse "^4.1.0"
    object-assign "^4.0.1"

estraverse@^4.0.0, estraverse@^4.1.0, estraverse@^4.1.1, estraverse@^4.2.0:
  version "4.2.0"
  resolved "https://registry.yarnpkg.com/estraverse/-/estraverse-4.2.0.tgz#0dee3fed31fcd469618ce7342099fc1afa0bdb13"

esutils@^2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/esutils/-/esutils-2.0.2.tgz#0abf4f1caa5bcb1f7a9d8acc6dea4faaa04bac9b"

etag@~1.8.1:
  version "1.8.1"
  resolved "https://registry.yarnpkg.com/etag/-/etag-1.8.1.tgz#41ae2eeb65efa62268aebfea83ac7d79299b0887"

express@^4.16.2:
  version "4.16.2"
  resolved "https://registry.yarnpkg.com/express/-/express-4.16.2.tgz#e35c6dfe2d64b7dca0a5cd4f21781be3299e076c"
  dependencies:
    accepts "~1.3.4"
    array-flatten "1.1.1"
    body-parser "1.18.2"
    content-disposition "0.5.2"
    content-type "~1.0.4"
    cookie "0.3.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "~1.1.1"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.1.0"
    fresh "0.5.2"
    merge-descriptors "1.0.1"
    methods "~1.1.2"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    path-to-regexp "0.1.7"
    proxy-addr "~2.0.2"
    qs "6.5.1"
    range-parser "~1.2.0"
    safe-buffer "5.1.1"
    send "0.16.1"
    serve-static "1.13.1"
    setprototypeof "1.1.0"
    statuses "~1.3.1"
    type-is "~1.6.15"
    utils-merge "1.0.1"
    vary "~1.1.2"

extend@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/extend/-/extend-3.0.1.tgz#a755ea7bc1adfcc5a31ce7e762dbaadc5e636444"

external-editor@^2.0.4:
  version "2.0.5"
  resolved "https://registry.yarnpkg.com/external-editor/-/external-editor-2.0.5.tgz#52c249a3981b9ba187c7cacf5beb50bf1d91a6bc"
  dependencies:
    iconv-lite "^0.4.17"
    jschardet "^1.4.2"
    tmp "^0.0.33"

fast-deep-equal@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fast-deep-equal/-/fast-deep-equal-1.0.0.tgz#96256a3bc975595eb36d82e9929d060d893439ff"

fast-json-stable-stringify@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/fast-json-stable-stringify/-/fast-json-stable-stringify-2.0.0.tgz#d5142c0caee6b1189f87d3a76111064f86c8bbf2"

fast-levenshtein@~2.0.4:
  version "2.0.6"
  resolved "https://registry.yarnpkg.com/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"

figures@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/figures/-/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/file-entry-cache/-/file-entry-cache-2.0.0.tgz#c392990c3e684783d838b8c84a45d8a048458361"
  dependencies:
    flat-cache "^1.2.1"
    object-assign "^4.0.1"

finalhandler@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/finalhandler/-/finalhandler-1.1.0.tgz#ce0b6855b45853e791b2fcc680046d88253dd7f5"
  dependencies:
    debug "2.6.9"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    on-finished "~2.3.0"
    parseurl "~1.3.2"
    statuses "~1.3.1"
    unpipe "~1.0.0"

find-up@^1.0.0:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-1.1.2.tgz#6b2e9822b1a2ce0a60ab64d610eccad53cb24d0f"
  dependencies:
    path-exists "^2.0.0"
    pinkie-promise "^2.0.0"

find-up@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/find-up/-/find-up-2.1.0.tgz#45d1b7e506c717ddd482775a2b77920a3c0c57a7"
  dependencies:
    locate-path "^2.0.0"

flat-cache@^1.2.1:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/flat-cache/-/flat-cache-1.3.0.tgz#d3030b32b38154f4e3b7e9c709f490f7ef97c481"
  dependencies:
    circular-json "^0.3.1"
    del "^2.0.2"
    graceful-fs "^4.1.2"
    write "^0.2.1"

form-data@^2.3.1:
  version "2.3.1"
  resolved "https://registry.yarnpkg.com/form-data/-/form-data-2.3.1.tgz#6fb94fbd71885306d73d15cc497fe4cc4ecd44bf"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.5"
    mime-types "^2.1.12"

formidable@^1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/formidable/-/formidable-1.1.1.tgz#96b8886f7c3c3508b932d6bd70c4d3a88f35f1a9"

forwarded@~0.1.2:
  version "0.1.2"
  resolved "https://registry.yarnpkg.com/forwarded/-/forwarded-0.1.2.tgz#98c23dab1175657b8c0573e8ceccd91b0ff18c84"

fresh@0.5.2:
  version "0.5.2"
  resolved "https://registry.yarnpkg.com/fresh/-/fresh-0.5.2.tgz#3d8cadd90d976569fa835ab1f8e4b23a105605a7"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"

function-bind@^1.0.2:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/function-bind/-/function-bind-1.1.1.tgz#a56899d3ea3c9bab874bb9773b7c5ede92f4895d"

functional-red-black-tree@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz#1b0ab3bd553b2a0d6399d29c0e3ea0b252078327"

glob@7.1.2, glob@^7.0.3, glob@^7.0.5, glob@^7.1.2:
  version "7.1.2"
  resolved "https://registry.yarnpkg.com/glob/-/glob-7.1.2.tgz#c19c9df9a028702d678612384a6552404c636d15"
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.0.4"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^9.17.0:
  version "9.18.0"
  resolved "https://registry.yarnpkg.com/globals/-/globals-9.18.0.tgz#aa3896b3e69b487f17e31ed2143d69a8e30c2d8a"

globby@^5.0.0:
  version "5.0.0"
  resolved "https://registry.yarnpkg.com/globby/-/globby-5.0.0.tgz#ebd84667ca0dbb330b99bcfc68eac2bc54370e0d"
  dependencies:
    array-union "^1.0.1"
    arrify "^1.0.0"
    glob "^7.0.3"
    object-assign "^4.0.1"
    pify "^2.0.0"
    pinkie-promise "^2.0.0"

graceful-fs@^4.1.2:
  version "4.1.11"
  resolved "https://registry.yarnpkg.com/graceful-fs/-/graceful-fs-4.1.11.tgz#0e8bdfe4d1ddb8854d64e04ea7c00e2a026e5658"

growl@1.10.3:
  version "1.10.3"
  resolved "https://registry.yarnpkg.com/growl/-/growl-1.10.3.tgz#1926ba90cf3edfe2adb4927f5880bc22c66c790f"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-ansi/-/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  dependencies:
    ansi-regex "^2.0.0"

has-flag@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/has-flag/-/has-flag-2.0.0.tgz#e8207af1cc7b30d446cc70b734b5e8be18f88d51"

has@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/has/-/has-1.0.1.tgz#8461733f538b0837c9361e39a9ab9e9704dc2f28"
  dependencies:
    function-bind "^1.0.2"

he@1.1.1:
  version "1.1.1"
  resolved "https://registry.yarnpkg.com/he/-/he-1.1.1.tgz#93410fd21b009735151f8868c2f271f3427e23fd"

hosted-git-info@^2.1.4:
  version "2.5.0"
  resolved "https://registry.yarnpkg.com/hosted-git-info/-/hosted-git-info-2.5.0.tgz#6d60e34b3abbc8313062c3b798ef8d901a07af3c"

http-errors@1.6.2, http-errors@~1.6.2:
  version "1.6.2"
  resolved "https://registry.yarnpkg.com/http-errors/-/http-errors-1.6.2.tgz#0a002cc85707192a7e7946ceedc11155f60ec736"
  dependencies:
    depd "1.1.1"
    inherits "2.0.3"
    setprototypeof "1.0.3"
    statuses ">= 1.3.1 < 2"

husky@^0.14.3:
  version "0.14.3"
  resolved "https://registry.yarnpkg.com/husky/-/husky-0.14.3.tgz#c69ed74e2d2779769a17ba8399b54ce0b63c12c3"
  dependencies:
    is-ci "^1.0.10"
    normalize-path "^1.0.0"
    strip-indent "^2.0.0"

iconv-lite@0.4.19, iconv-lite@^0.4.17:
  version "0.4.19"
  resolved "https://registry.yarnpkg.com/iconv-lite/-/iconv-lite-0.4.19.tgz#f7468f60135f5e5dad3399c0a81be9a1603a082b"

ignore@^3.3.3:
  version "3.3.7"
  resolved "https://registry.yarnpkg.com/ignore/-/ignore-3.3.7.tgz#612289bfb3c220e186a58118618d5be8c1bab021"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://registry.yarnpkg.com/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://registry.yarnpkg.com/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@2.0.3, inherits@^2.0.3, inherits@~2.0.3:
  version "2.0.3"
  resolved "https://registry.yarnpkg.com/inherits/-/inherits-2.0.3.tgz#633c2c83e3da42a502f52466022480f4208261de"

inquirer@^3.0.6:
  version "3.3.0"
  resolved "https://registry.yarnpkg.com/inquirer/-/inquirer-3.3.0.tgz#9dd2f2ad765dcab1ff0443b491442a20ba227dc9"
  dependencies:
    ansi-escapes "^3.0.0"
    chalk "^2.0.0"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^2.0.4"
    figures "^2.0.0"
    lodash "^4.3.0"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rx-lite "^4.0.8"
    rx-lite-aggregates "^4.0.8"
    string-width "^2.1.0"
    strip-ansi "^4.0.0"
    through "^2.3.6"

ipaddr.js@1.5.2:
  version "1.5.2"
  resolved "https://registry.yarnpkg.com/ipaddr.js/-/ipaddr.js-1.5.2.tgz#d4b505bde9946987ccf0fc58d9010ff9607e3fa0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"

is-builtin-module@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-builtin-module/-/is-builtin-module-1.0.0.tgz#540572d34f7ac3119f8f76c30cbc1b1e037affbe"
  dependencies:
    builtin-modules "^1.0.0"

is-ci@^1.0.10:
  version "1.0.10"
  resolved "https://registry.yarnpkg.com/is-ci/-/is-ci-1.0.10.tgz#f739336b2632365061a9d48270cd56ae3369318e"
  dependencies:
    ci-info "^1.0.0"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"

is-path-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-cwd/-/is-path-cwd-1.0.0.tgz#d225ec23132e89edd38fda767472e62e65f1106d"

is-path-in-cwd@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-in-cwd/-/is-path-in-cwd-1.0.0.tgz#6477582b8214d602346094567003be8a9eac04dc"
  dependencies:
    is-path-inside "^1.0.0"

is-path-inside@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-path-inside/-/is-path-inside-1.0.0.tgz#fc06e5a1683fbda13de667aff717bbc10a48f37f"
  dependencies:
    path-is-inside "^1.0.1"

is-promise@^2.1.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/is-promise/-/is-promise-2.1.0.tgz#79a2a9ece7f096e80f36d2b2f3bc16c1ff4bf3fa"

is-resolvable@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/is-resolvable/-/is-resolvable-1.0.0.tgz#8df57c61ea2e3c501408d100fb013cf8d6e0cc62"
  dependencies:
    tryit "^1.0.1"

isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"

js-tokens@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/js-tokens/-/js-tokens-3.0.2.tgz#9866df395102130e38f7f996bceb65443209c25b"

js-yaml@^3.9.1:
  version "3.10.0"
  resolved "https://registry.yarnpkg.com/js-yaml/-/js-yaml-3.10.0.tgz#2e78441646bd4682e963f22b6e92823c309c62dc"
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

jschardet@^1.4.2:
  version "1.6.0"
  resolved "https://registry.yarnpkg.com/jschardet/-/jschardet-1.6.0.tgz#c7d1a71edcff2839db2f9ec30fc5d5ebd3c1a678"

json-schema-traverse@^0.3.0:
  version "0.3.1"
  resolved "https://registry.yarnpkg.com/json-schema-traverse/-/json-schema-traverse-0.3.1.tgz#349a6d44c53a51de89b40805c5d5e59b417d3340"

json-stable-stringify@^1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/json-stable-stringify/-/json-stable-stringify-1.0.1.tgz#9a759d39c5f2ff503fd5300646ed445f88c4f9af"
  dependencies:
    jsonify "~0.0.0"

jsonify@~0.0.0:
  version "0.0.0"
  resolved "https://registry.yarnpkg.com/jsonify/-/jsonify-0.0.0.tgz#2c74b6ee41d93ca51b7b5aaee8f503631d252a73"

levn@^0.3.0, levn@~0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/levn/-/levn-0.3.0.tgz#3b09924edf9f083c0490fdd4c0bc4421e04764ee"
  dependencies:
    prelude-ls "~1.1.2"
    type-check "~0.3.2"

load-json-file@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/load-json-file/-/load-json-file-2.0.0.tgz#7947e42149af80d696cbf797bcaabcfe1fe29ca8"
  dependencies:
    graceful-fs "^4.1.2"
    parse-json "^2.2.0"
    pify "^2.0.0"
    strip-bom "^3.0.0"

locate-path@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/locate-path/-/locate-path-2.0.0.tgz#2b568b265eec944c6d9c0de9c3dbbbca0354cd8e"
  dependencies:
    p-locate "^2.0.0"
    path-exists "^3.0.0"

lodash.cond@^4.3.0:
  version "4.5.2"
  resolved "https://registry.yarnpkg.com/lodash.cond/-/lodash.cond-4.5.2.tgz#f471a1da486be60f6ab955d17115523dd1d255d5"

lodash@^4.17.4, lodash@^4.3.0:
  version "4.17.4"
  resolved "https://registry.yarnpkg.com/lodash/-/lodash-4.17.4.tgz#78203a4d1c328ae1d86dca6460e369b57f4055ae"

lru-cache@^4.0.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/lru-cache/-/lru-cache-4.1.1.tgz#622e32e82488b49279114a4f9ecf45e7cd6bba55"
  dependencies:
    pseudomap "^1.0.2"
    yallist "^2.1.2"

media-typer@0.3.0:
  version "0.3.0"
  resolved "https://registry.yarnpkg.com/media-typer/-/media-typer-0.3.0.tgz#8710d7af0aa626f8fffa1ce00168545263255748"

merge-descriptors@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/merge-descriptors/-/merge-descriptors-1.0.1.tgz#b00aaa556dd8b44568150ec9d1b953f3f90cbb61"

methods@^1.1.1, methods@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/methods/-/methods-1.1.2.tgz#5529a4d67654134edcc5266656835b0f851afcee"

mime-db@~1.30.0:
  version "1.30.0"
  resolved "https://registry.yarnpkg.com/mime-db/-/mime-db-1.30.0.tgz#74c643da2dd9d6a45399963465b26d5ca7d71f01"

mime-types@^2.1.12, mime-types@~2.1.15, mime-types@~2.1.16:
  version "2.1.17"
  resolved "https://registry.yarnpkg.com/mime-types/-/mime-types-2.1.17.tgz#09d7a393f03e995a79f8af857b70a9e0ab16557a"
  dependencies:
    mime-db "~1.30.0"

mime@1.4.1, mime@^1.4.1:
  version "1.4.1"
  resolved "https://registry.yarnpkg.com/mime/-/mime-1.4.1.tgz#121f9ebc49e3766f311a76e1fa1c8003c4b03aa6"

mimic-fn@^1.0.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/mimic-fn/-/mimic-fn-1.1.0.tgz#e667783d92e89dbd342818b5230b9d62a672ad18"

minimatch@^3.0.2, minimatch@^3.0.3, minimatch@^3.0.4:
  version "3.0.4"
  resolved "https://registry.yarnpkg.com/minimatch/-/minimatch-3.0.4.tgz#5166e286457f03306064be5497e8dbb0c3d32083"
  dependencies:
    brace-expansion "^1.1.7"

minimist@0.0.8:
  version "0.0.8"
  resolved "https://registry.yarnpkg.com/minimist/-/minimist-0.0.8.tgz#857fcabfc3397d2625b8228262e86aa7a011b05d"

mkdirp@0.5.1, mkdirp@^0.5.1:
  version "0.5.1"
  resolved "https://registry.yarnpkg.com/mkdirp/-/mkdirp-0.5.1.tgz#30057438eac6cf7f8c4767f38648d6697d75c903"
  dependencies:
    minimist "0.0.8"

mocha@^4.0.1:
  version "4.0.1"
  resolved "https://registry.yarnpkg.com/mocha/-/mocha-4.0.1.tgz#0aee5a95cf69a4618820f5e51fa31717117daf1b"
  dependencies:
    browser-stdout "1.3.0"
    commander "2.11.0"
    debug "3.1.0"
    diff "3.3.1"
    escape-string-regexp "1.0.5"
    glob "7.1.2"
    growl "1.10.3"
    he "1.1.1"
    mkdirp "0.5.1"
    supports-color "4.4.0"

ms@2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "https://registry.yarnpkg.com/mute-stream/-/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"

negotiator@0.6.1:
  version "0.6.1"
  resolved "https://registry.yarnpkg.com/negotiator/-/negotiator-0.6.1.tgz#2b327184e8992101177b28563fb5e7102acd0ca9"

normalize-package-data@^2.3.2:
  version "2.4.0"
  resolved "https://registry.yarnpkg.com/normalize-package-data/-/normalize-package-data-2.4.0.tgz#12f95a307d58352075a04907b84ac8be98ac012f"
  dependencies:
    hosted-git-info "^2.1.4"
    is-builtin-module "^1.0.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/normalize-path/-/normalize-path-1.0.0.tgz#32d0e472f91ff345701c15a8311018d3b0a90379"

object-assign@^4.0.1:
  version "4.1.1"
  resolved "https://registry.yarnpkg.com/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"

on-finished@~2.3.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/on-finished/-/on-finished-2.3.0.tgz#20f1336481b083cd75337992a16971aa2d906947"
  dependencies:
    ee-first "1.1.1"

once@^1.3.0:
  version "1.4.0"
  resolved "https://registry.yarnpkg.com/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/onetime/-/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  dependencies:
    mimic-fn "^1.0.0"

optionator@^0.8.2:
  version "0.8.2"
  resolved "https://registry.yarnpkg.com/optionator/-/optionator-0.8.2.tgz#364c5e409d3f4d6301d6c0b4c05bba50180aeb64"
  dependencies:
    deep-is "~0.1.3"
    fast-levenshtein "~2.0.4"
    levn "~0.3.0"
    prelude-ls "~1.1.2"
    type-check "~0.3.2"
    wordwrap "~1.0.0"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"

p-limit@^1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/p-limit/-/p-limit-1.1.0.tgz#b07ff2d9a5d88bec806035895a2bab66a27988bc"

p-locate@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/p-locate/-/p-locate-2.0.0.tgz#20a0103b222a70c8fd39cc2e580680f3dde5ec43"
  dependencies:
    p-limit "^1.1.0"

parse-json@^2.2.0:
  version "2.2.0"
  resolved "https://registry.yarnpkg.com/parse-json/-/parse-json-2.2.0.tgz#f480f40434ef80741f8469099f8dea18f55a4dc9"
  dependencies:
    error-ex "^1.2.0"

parseurl@~1.3.2:
  version "1.3.2"
  resolved "https://registry.yarnpkg.com/parseurl/-/parseurl-1.3.2.tgz#fc289d4ed8993119460c156253262cdc8de65bf3"

path-exists@^2.0.0:
  version "2.1.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-2.1.0.tgz#0feb6c64f0fc518d9a754dd5efb62c7022761f4b"
  dependencies:
    pinkie-promise "^2.0.0"

path-exists@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/path-exists/-/path-exists-3.0.0.tgz#ce0ebeaa5f78cb18925ea7d810d7b59b010fd515"

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"

path-is-inside@^1.0.1, path-is-inside@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/path-is-inside/-/path-is-inside-1.0.2.tgz#365417dede44430d1c11af61027facf074bdfc53"

path-parse@^1.0.5:
  version "1.0.5"
  resolved "https://registry.yarnpkg.com/path-parse/-/path-parse-1.0.5.tgz#3c1adf871ea9cd6c9431b6ea2bd74a0ff055c4c1"

path-to-regexp@0.1.7:
  version "0.1.7"
  resolved "https://registry.yarnpkg.com/path-to-regexp/-/path-to-regexp-0.1.7.tgz#df604178005f522f15eb4490e7247a1bfaa67f8c"

path-type@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/path-type/-/path-type-2.0.0.tgz#f012ccb8415b7096fc2daa1054c3d72389594c73"
  dependencies:
    pify "^2.0.0"

pify@^2.0.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"

pinkie-promise@^2.0.0:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/pinkie-promise/-/pinkie-promise-2.0.1.tgz#2135d6dfa7a358c069ac9b178776288228450ffa"
  dependencies:
    pinkie "^2.0.0"

pinkie@^2.0.0:
  version "2.0.4"
  resolved "https://registry.yarnpkg.com/pinkie/-/pinkie-2.0.4.tgz#72556b80cfa0d48a974e80e77248e80ed4f7f870"

pkg-dir@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/pkg-dir/-/pkg-dir-1.0.0.tgz#7a4b508a8d5bb2d629d447056ff4e9c9314cf3d4"
  dependencies:
    find-up "^1.0.0"

pluralize@^7.0.0:
  version "7.0.0"
  resolved "https://registry.yarnpkg.com/pluralize/-/pluralize-7.0.0.tgz#298b89df8b93b0221dbf421ad2b1b1ea23fc6777"

prelude-ls@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/prelude-ls/-/prelude-ls-1.1.2.tgz#21932a549f5e52ffd9a827f570e04be62a97da54"

process-nextick-args@~1.0.6:
  version "1.0.7"
  resolved "https://registry.yarnpkg.com/process-nextick-args/-/process-nextick-args-1.0.7.tgz#150e20b756590ad3f91093f25a4f2ad8bff30ba3"

progress@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/progress/-/progress-2.0.0.tgz#8a1be366bf8fc23db2bd23f10c6fe920b4389d1f"

proxy-addr@~2.0.2:
  version "2.0.2"
  resolved "https://registry.yarnpkg.com/proxy-addr/-/proxy-addr-2.0.2.tgz#6571504f47bb988ec8180253f85dd7e14952bdec"
  dependencies:
    forwarded "~0.1.2"
    ipaddr.js "1.5.2"

pseudomap@^1.0.2:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/pseudomap/-/pseudomap-1.0.2.tgz#f052a28da70e618917ef0a8ac34c1ae5a68286b3"

qs@6.5.1, qs@^6.5.1:
  version "6.5.1"
  resolved "https://registry.yarnpkg.com/qs/-/qs-6.5.1.tgz#349cdf6eef89ec45c12d7d5eb3fc0c870343a6d8"

range-parser@~1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/range-parser/-/range-parser-1.2.0.tgz#f49be6b487894ddc40dcc94a322f611092e00d5e"

raw-body@2.3.2:
  version "2.3.2"
  resolved "https://registry.yarnpkg.com/raw-body/-/raw-body-2.3.2.tgz#bcd60c77d3eb93cde0050295c3f379389bc88f89"
  dependencies:
    bytes "3.0.0"
    http-errors "1.6.2"
    iconv-lite "0.4.19"
    unpipe "1.0.0"

read-pkg-up@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/read-pkg-up/-/read-pkg-up-2.0.0.tgz#6b72a8048984e0c41e79510fd5e9fa99b3b549be"
  dependencies:
    find-up "^2.0.0"
    read-pkg "^2.0.0"

read-pkg@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/read-pkg/-/read-pkg-2.0.0.tgz#8ef1c0623c6a6db0dc6713c4bfac46332b2368f8"
  dependencies:
    load-json-file "^2.0.0"
    normalize-package-data "^2.3.2"
    path-type "^2.0.0"

readable-stream@^2.0.5, readable-stream@^2.2.2:
  version "2.3.3"
  resolved "https://registry.yarnpkg.com/readable-stream/-/readable-stream-2.3.3.tgz#368f2512d79f9d46fdfc71349ae7878bbc1eb95c"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~1.0.6"
    safe-buffer "~5.1.1"
    string_decoder "~1.0.3"
    util-deprecate "~1.0.1"

require-uncached@^1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/require-uncached/-/require-uncached-1.0.3.tgz#4e0d56d6c9662fd31e43011c4b95aa49955421d3"
  dependencies:
    caller-path "^0.1.0"
    resolve-from "^1.0.0"

resolve-from@^1.0.0:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/resolve-from/-/resolve-from-1.0.1.tgz#26cbfe935d1aeeeabb29bc3fe5aeb01e93d44226"

resolve@^1.2.0:
  version "1.5.0"
  resolved "https://registry.yarnpkg.com/resolve/-/resolve-1.5.0.tgz#1f09acce796c9a762579f31b2c1cc4c3cddf9f36"
  dependencies:
    path-parse "^1.0.5"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/restore-cursor/-/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

rimraf@^2.2.8:
  version "2.6.2"
  resolved "https://registry.yarnpkg.com/rimraf/-/rimraf-2.6.2.tgz#2ed8150d24a16ea8651e6d6ef0f47c4158ce7a36"
  dependencies:
    glob "^7.0.5"

run-async@^2.2.0:
  version "2.3.0"
  resolved "https://registry.yarnpkg.com/run-async/-/run-async-2.3.0.tgz#0371ab4ae0bdd720d4166d7dfda64ff7a445a6c0"
  dependencies:
    is-promise "^2.1.0"

rx-lite-aggregates@^4.0.8:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/rx-lite-aggregates/-/rx-lite-aggregates-4.0.8.tgz#753b87a89a11c95467c4ac1626c4efc4e05c67be"
  dependencies:
    rx-lite "*"

rx-lite@*, rx-lite@^4.0.8:
  version "4.0.8"
  resolved "https://registry.yarnpkg.com/rx-lite/-/rx-lite-4.0.8.tgz#0b1e11af8bc44836f04a6407e92da42467b79444"

safe-buffer@5.1.1, safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.1"
  resolved "https://registry.yarnpkg.com/safe-buffer/-/safe-buffer-5.1.1.tgz#893312af69b2123def71f57889001671eeb2c853"

"semver@2 || 3 || 4 || 5", semver@^5.3.0:
  version "5.4.1"
  resolved "https://registry.yarnpkg.com/semver/-/semver-5.4.1.tgz#e059c09d8571f0540823733433505d3a2f00b18e"

send@0.16.1:
  version "0.16.1"
  resolved "https://registry.yarnpkg.com/send/-/send-0.16.1.tgz#a70e1ca21d1382c11d0d9f6231deb281080d7ab3"
  dependencies:
    debug "2.6.9"
    depd "~1.1.1"
    destroy "~1.0.4"
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "~1.6.2"
    mime "1.4.1"
    ms "2.0.0"
    on-finished "~2.3.0"
    range-parser "~1.2.0"
    statuses "~1.3.1"

serve-static@1.13.1:
  version "1.13.1"
  resolved "https://registry.yarnpkg.com/serve-static/-/serve-static-1.13.1.tgz#4c57d53404a761d8f2e7c1e8a18a47dbf278a719"
  dependencies:
    encodeurl "~1.0.1"
    escape-html "~1.0.3"
    parseurl "~1.3.2"
    send "0.16.1"

setprototypeof@1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.0.3.tgz#66567e37043eeb4f04d91bd658c0cbefb55b8e04"

setprototypeof@1.1.0:
  version "1.1.0"
  resolved "https://registry.yarnpkg.com/setprototypeof/-/setprototypeof-1.1.0.tgz#d0bd85536887b6fe7c0d818cb962d9d91c54e656"

shebang-command@^1.2.0:
  version "1.2.0"
  resolved "https://registry.yarnpkg.com/shebang-command/-/shebang-command-1.2.0.tgz#44aac65b695b03398968c39f363fee5deafdf1ea"
  dependencies:
    shebang-regex "^1.0.0"

shebang-regex@^1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/shebang-regex/-/shebang-regex-1.0.0.tgz#da42f49740c0b42db2ca9728571cb190c98efea3"

signal-exit@^3.0.2:
  version "3.0.2"
  resolved "https://registry.yarnpkg.com/signal-exit/-/signal-exit-3.0.2.tgz#b5fdc08f1287ea1178628e415e25132b73646c6d"

slice-ansi@1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/slice-ansi/-/slice-ansi-1.0.0.tgz#044f1a49d8842ff307aad6b505ed178bd950134d"
  dependencies:
    is-fullwidth-code-point "^2.0.0"

spdx-correct@~1.0.0:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/spdx-correct/-/spdx-correct-1.0.2.tgz#4b3073d933ff51f3912f03ac5519498a4150db40"
  dependencies:
    spdx-license-ids "^1.0.2"

spdx-expression-parse@~1.0.0:
  version "1.0.4"
  resolved "https://registry.yarnpkg.com/spdx-expression-parse/-/spdx-expression-parse-1.0.4.tgz#9bdf2f20e1f40ed447fbe273266191fced51626c"

spdx-license-ids@^1.0.2:
  version "1.2.2"
  resolved "https://registry.yarnpkg.com/spdx-license-ids/-/spdx-license-ids-1.2.2.tgz#c9df7a3424594ade6bd11900d596696dc06bac57"

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"

"statuses@>= 1.3.1 < 2", statuses@~1.3.1:
  version "1.3.1"
  resolved "https://registry.yarnpkg.com/statuses/-/statuses-1.3.1.tgz#faf51b9eb74aaef3b3acf4ad5f61abf24cb7b93e"

string-width@^2.1.0, string-width@^2.1.1:
  version "2.1.1"
  resolved "https://registry.yarnpkg.com/string-width/-/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string_decoder@~1.0.3:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/string_decoder/-/string_decoder-1.0.3.tgz#0fc67d7c141825de94282dd536bec6b9bce860ab"
  dependencies:
    safe-buffer "~5.1.0"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "https://registry.yarnpkg.com/strip-ansi/-/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  dependencies:
    ansi-regex "^3.0.0"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"

strip-indent@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/strip-indent/-/strip-indent-2.0.0.tgz#5ef8db295d01e6ed6cbf7aab96998d7822527b68"

strip-json-comments@~2.0.1:
  version "2.0.1"
  resolved "https://registry.yarnpkg.com/strip-json-comments/-/strip-json-comments-2.0.1.tgz#3c531942e908c2697c0ec344858c286c7ca0a60a"

superagent@^3.0.0:
  version "3.8.0"
  resolved "https://registry.yarnpkg.com/superagent/-/superagent-3.8.0.tgz#87e3ed536c8860c08c7c7d1225da9a80cab064c6"
  dependencies:
    component-emitter "^1.2.0"
    cookiejar "^2.1.0"
    debug "^3.1.0"
    extend "^3.0.0"
    form-data "^2.3.1"
    formidable "^1.1.1"
    methods "^1.1.1"
    mime "^1.4.1"
    qs "^6.5.1"
    readable-stream "^2.0.5"

supertest@^3.0.0:
  version "3.0.0"
  resolved "https://registry.yarnpkg.com/supertest/-/supertest-3.0.0.tgz#8d4bb68fd1830ee07033b1c5a5a9a4021c965296"
  dependencies:
    methods "~1.1.2"
    superagent "^3.0.0"

supports-color@4.4.0, supports-color@^4.0.0:
  version "4.4.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-4.4.0.tgz#883f7ddabc165142b2a61427f3352ded195d1a3e"
  dependencies:
    has-flag "^2.0.0"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "https://registry.yarnpkg.com/supports-color/-/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"

table@^4.0.1:
  version "4.0.2"
  resolved "https://registry.yarnpkg.com/table/-/table-4.0.2.tgz#a33447375391e766ad34d3486e6e2aedc84d2e36"
  dependencies:
    ajv "^5.2.3"
    ajv-keywords "^2.1.0"
    chalk "^2.1.0"
    lodash "^4.17.4"
    slice-ansi "1.0.0"
    string-width "^2.1.1"

text-table@~0.2.0:
  version "0.2.0"
  resolved "https://registry.yarnpkg.com/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"

through@^2.3.6:
  version "2.3.8"
  resolved "https://registry.yarnpkg.com/through/-/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://registry.yarnpkg.com/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  dependencies:
    os-tmpdir "~1.0.2"

tryit@^1.0.1:
  version "1.0.3"
  resolved "https://registry.yarnpkg.com/tryit/-/tryit-1.0.3.tgz#393be730a9446fd1ead6da59a014308f36c289cb"

type-check@~0.3.2:
  version "0.3.2"
  resolved "https://registry.yarnpkg.com/type-check/-/type-check-0.3.2.tgz#5884cab512cf1d355e3fb784f30804b2b520db72"
  dependencies:
    prelude-ls "~1.1.2"

type-is@~1.6.15:
  version "1.6.15"
  resolved "https://registry.yarnpkg.com/type-is/-/type-is-1.6.15.tgz#cab10fb4909e441c82842eafe1ad646c81804410"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.15"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://registry.yarnpkg.com/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"

unpipe@1.0.0, unpipe@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/unpipe/-/unpipe-1.0.0.tgz#b2bf4ee8514aae6165b4817829d21b2ef49904ec"

util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"

utils-merge@1.0.1:
  version "1.0.1"
  resolved "https://registry.yarnpkg.com/utils-merge/-/utils-merge-1.0.1.tgz#9f95710f50a267947b2ccc124741c1028427e713"

validate-npm-package-license@^3.0.1:
  version "3.0.1"
  resolved "https://registry.yarnpkg.com/validate-npm-package-license/-/validate-npm-package-license-3.0.1.tgz#2804babe712ad3379459acfbe24746ab2c303fbc"
  dependencies:
    spdx-correct "~1.0.0"
    spdx-expression-parse "~1.0.0"

vary@~1.1.2:
  version "1.1.2"
  resolved "https://registry.yarnpkg.com/vary/-/vary-1.1.2.tgz#2299f02c6ded30d4a5961b0b9f74524a18f634fc"

which@^1.2.9:
  version "1.3.0"
  resolved "https://registry.yarnpkg.com/which/-/which-1.3.0.tgz#ff04bdfc010ee547d780bec38e1ac1c2777d253a"
  dependencies:
    isexe "^2.0.0"

wordwrap@~1.0.0:
  version "1.0.0"
  resolved "https://registry.yarnpkg.com/wordwrap/-/wordwrap-1.0.0.tgz#27584810891456a4171c8d0226441ade90cbcaeb"

wrappy@1:
  version "1.0.2"
  resolved "https://registry.yarnpkg.com/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"

write@^0.2.1:
  version "0.2.1"
  resolved "https://registry.yarnpkg.com/write/-/write-0.2.1.tgz#5fc03828e264cea3fe91455476f7a3c566cb0757"
  dependencies:
    mkdirp "^0.5.1"

yallist@^2.1.2:
  version "2.1.2"
  resolved "https://registry.yarnpkg.com/yallist/-/yallist-2.1.2.tgz#1c11f9218f076089a47dd512f93c6699a6a81d52"
