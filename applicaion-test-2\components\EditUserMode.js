import React, { useRef, useState } from 'react';
import { Modal, Button, FormControl, Input, Text } from 'native-base';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useStore } from '../libs/state';
import axios from 'axios';
import { API_URL } from '@env';

export default function EditUserModal(props) {
  const { user, setUser, accessToken } = useStore();
  const initialModalRef = useRef(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const formik = useFormik({
    initialValues: {
      name: user?.name || '',
      email: user?.email || '',
      status: user?.status || '',
    },
    validationSchema: Yup.object({
      name: Yup.string().required("Name is required"),
      email: Yup.string().required("Email is required").email("Invalid email address"),
      status: Yup.string().required("Status is required"),
    }),
    onSubmit: async (values) => {
      if (!accessToken) {
        setError("Authorization accessToken is missing");
        return;
      }

      try {
        setLoading(true);
        const response = await axios.put(`${API_URL}/user`, values, {
          headers: {
            Authorization: token,
          },
        });
        setUser(response.data.user);
        props.closeModal();
        console.log("User updated successfully:", response.data.user);
        alert("User updated successfully!");
      } catch (error) {
        console.error("Error updating user:", error);
        setError("Failed to update user. Please try again.");
      } finally {
        setLoading(false);
      }
    },
  });

  if (!user) return null;

  return (
    <Modal
      isOpen={props.modalVisible}
      onClose={props.closeModal}
      initialFocusRef={initialModalRef}
    >
      <Modal.Content>
        <Modal.CloseButton />
        <Modal.Header>Edit User</Modal.Header>
        <Modal.Body>
          <FormControl isInvalid={formik.errors.name && formik.touched.name}>
            <FormControl.Label>Name</FormControl.Label>
            <Input
              ref={initialModalRef}
              value={formik.values.name}
              onChangeText={formik.handleChange('name')}
              onBlur={formik.handleBlur('name')}
            />
            <FormControl.ErrorMessage>{formik.errors.name}</FormControl.ErrorMessage>
          </FormControl>

          <FormControl mt={3} isInvalid={formik.errors.email && formik.touched.email}>
            <FormControl.Label>Email</FormControl.Label>
            <Input
              value={formik.values.email}
              onChangeText={formik.handleChange('email')}
              onBlur={formik.handleBlur('email')}
            />
            <FormControl.ErrorMessage>{formik.errors.email}</FormControl.ErrorMessage>
          </FormControl>

          <FormControl mt={3} isInvalid={formik.errors.status && formik.touched.status}>
            <FormControl.Label>Status</FormControl.Label>
            <Input
              value={formik.values.status}
              onChangeText={formik.handleChange('status')}
              onBlur={formik.handleBlur('status')}
            />
            <FormControl.ErrorMessage>{formik.errors.status}</FormControl.ErrorMessage>
          </FormControl>

          {error && <Text color="red.500" mt={2}>{error}</Text>}
        </Modal.Body>
        <Modal.Footer>
          <Button.Group space={2}>
            <Button variant="ghost" onPress={props.closeModal}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onPress={formik.handleSubmit}
              isLoading={loading}
            >
              Save
            </Button>
          </Button.Group>
        </Modal.Footer>
      </Modal.Content>
    </Modal>
  );
}
