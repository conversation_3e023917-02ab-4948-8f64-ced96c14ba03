{"name": "applicaion-test-2", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start --clear", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~4.0.0", "@react-native-async-storage/async-storage": "^2.1.0", "@react-navigation/material-top-tabs": "^7.1.0", "@react-navigation/native": "^7.0.14", "@react-navigation/native-stack": "^7.2.0", "axios": "^1.7.9", "expo": "~52.0.24", "expo-image-picker": "^16.0.4", "expo-status-bar": "~2.0.0", "formik": "^2.4.6", "moment": "^2.30.1", "native-base": "^3.4.28", "normalize-css-color": "^1.0.2", "react": "18.3.1", "react-native": "0.76.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-haptic-feedback": "^2.3.3", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "^15.8.0", "react-native-web": "~0.19.13", "socket.io-client": "^4.8.1", "yup": "^1.6.1", "zustand": "^5.0.3", "expo-file-system": "~18.0.7"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}