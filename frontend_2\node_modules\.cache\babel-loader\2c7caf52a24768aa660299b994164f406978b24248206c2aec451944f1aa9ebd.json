{"ast": null, "code": "var _jsxFileName = \"D:\\\\my Programing\\\\whatsapp-clone-2\\\\frontend_2\\\\src\\\\components\\\\Sidebar\\\\Sidebar.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { FaSearch } from 'react-icons/fa'; // import the search icon from react-icons/fa\nimport { IoFilter } from 'react-icons/io5'; // import the filter icon from react-icons/io5\nimport { useStore } from \"../../libs/state\";\nimport MessageItem from './MessageItem';\nimport { useState } from 'react';\nimport Profile from '../Profile/Profile'; // import the Profile component\nimport { getReceiverMessages } from '../../libs/functions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Sidebar() {\n  _s();\n  var _friends$filter;\n  const {\n    friends,\n    messages,\n    user\n  } = useStore();\n  const [showProfile, setShowProfile] = useState(false);\n  const [search, setSearch] = useState('');\n  const [showUnseenMessages, setShowUnseenMessages] = useState(false);\n  const handleSearch = value => {\n    const fullName = `${value.username} `;\n    return fullName.toLowerCase().includes(search.toLowerCase());\n  };\n  if (showProfile) {\n    return /*#__PURE__*/_jsxDEV(Profile, {\n      onClose: () => setShowProfile(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 12\n    }, this);\n  }\n  const unreadMessagesContent = contact => {\n    if (!showUnseenMessages) return true;\n    const contactMessages = getReceiverMessages(Messages, contact._id);\n    const containUnseenMessages = contactMessages.some(message => !message.seen);\n    return containUnseenMessages;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex-none w-72 bg-gray-800 text-white\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between p-4 border-b border-gray-700\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: \"https://img.icons8.com/ios_filled/512/whatsapp.png\",\n          alt: \"avatar\",\n          className: \"w-8 h-8 rounded-full cursor-pointer\",\n          onClick: () => setShowProfile(true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"cursor-pointer text-white text-sm\",\n          children: user === null || user === void 0 ? void 0 : user.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 flex justify-between it\\r ems-center space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          placeholder: \"search\",\n          value: search,\n          onChange: e => setSearch(e.target.value),\n          className: \"w-full bg-gray-700 text-white rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-opacity-50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(FaSearch, {\n          className: \"absolute top-2 right-2 text-gray-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => {\n          setShowUnseenMessages(val => !val);\n        },\n        children: /*#__PURE__*/_jsxDEV(IoFilter, {\n          className: \"text-red-500\",\n          size: 20\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4 space-y-2\",\n      children: (friends === null || friends === void 0 ? void 0 : friends.length) > 0 ? friends === null || friends === void 0 ? void 0 : (_friends$filter = friends.filter(handleSearch) // تصفية حسب البحث\n      ) === null || _friends$filter === void 0 ? void 0 : _friends$filter.filter(unreadMessagesContent) // تصفية الرسائل غير المقروءة\n      .map(friend => /*#__PURE__*/_jsxDEV(MessageItem, {\n        sender: friend.username,\n        profilePicture: friend.profilePicture,\n        id: friend._id\n      }, friend._id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center text-gray-500\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"No Frindes found.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 5\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 9\n  }, this);\n}\n_s(Sidebar, \"c3aT+zxPE392g6FGTJKniQEpr6g=\", false, function () {\n  return [useStore];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "FaSearch", "<PERSON>o<PERSON><PERSON><PERSON>", "useStore", "MessageItem", "useState", "Profile", "getReceiverMessages", "jsxDEV", "_jsxDEV", "Sidebar", "_s", "_friends$filter", "friends", "messages", "user", "showProfile", "setShowProfile", "search", "setSearch", "showUnseenMessages", "setShowUnseenMessages", "handleSearch", "value", "fullName", "username", "toLowerCase", "includes", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "unreadMessagesContent", "contact", "contactMessages", "Messages", "_id", "containUnseenMessages", "some", "message", "seen", "className", "children", "src", "alt", "onClick", "type", "placeholder", "onChange", "e", "target", "val", "size", "length", "filter", "map", "friend", "sender", "profilePicture", "id", "_c", "$RefreshReg$"], "sources": ["D:/my Programing/whatsapp-clone-2/frontend_2/src/components/Sidebar/Sidebar.jsx"], "sourcesContent": ["import React from 'react';\r\nimport { FaSearch } from 'react-icons/fa'; // import the search icon from react-icons/fa\r\nimport { IoFilter } from 'react-icons/io5'; // import the filter icon from react-icons/io5\r\nimport { useStore } from \"../../libs/state\";\r\nimport MessageItem from './MessageItem';\r\nimport { useState } from 'react';\r\nimport Profile from '../Profile/Profile'; // import the Profile component\r\nimport { getReceiverMessages } from '../../libs/functions';\r\n\r\nexport default function Sidebar() {\r\n    const { friends , messages , user } = useStore();\r\n\r\n    const [showProfile, setShowProfile] = useState(false);\r\n    const [search, setSearch] = useState('');\r\n    const [showUnseenMessages, setShowUnseenMessages] = useState(false);\r\n\r\n\r\n\r\n    const handleSearch = (value) => {\r\n       const fullName = `${value.username} `;\r\n       return  fullName.toLowerCase().includes(search.toLowerCase());\r\n    }\r\n\r\n\r\n    if (showProfile) {\r\n    return <Profile\r\n    onClose={() => setShowProfile(false)} />;\r\n    }\r\n\r\n    const unreadMessagesContent = contact => {\r\n        if(!showUnseenMessages) return true;\r\n\r\n        const contactMessages = getReceiverMessages(Messages , contact._id);\r\n\r\n        const containUnseenMessages = contactMessages.some((message) => !message.seen );\r\n\r\n        return containUnseenMessages;\r\n    }\r\n\r\n    return (\r\n        <div className='flex-none w-72 bg-gray-800 text-white'>\r\n            <div className='flex items-center justify-between p-4 border-b border-gray-700'>\r\n                <div className='flex items-center space-x-2'>\r\n                    <img src='https://img.icons8.com/ios_filled/512/whatsapp.png'\r\n                     alt='avatar'\r\n                     className='w-8 h-8 rounded-full cursor-pointer'\r\n                     onClick={() => setShowProfile(true)}/>\r\n\r\n                </div>\r\n                <div className='flex items-center space-x-2'>\r\n                    <p className='cursor-pointer text-white text-sm'>\r\n                        {user?.username}\r\n                    </p>\r\n                </div>\r\n            </div>\r\n            <div className='p-4 flex justify-between it\r\n            ems-center space-x-3'>\r\n                <div className='relative flex-1'>\r\n                    <input type='text' placeholder='search'  value={search} onChange={e => setSearch(e.target.value)} className='w-full bg-gray-700 text-white rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-opacity-50'  />\r\n                    <FaSearch className='absolute top-2 right-2 text-gray-500' />\r\n                </div>\r\n                <button onClick={() => {setShowUnseenMessages((val) => !val)}}>\r\n                    <IoFilter className='text-red-500' size={20} />\r\n                </button>\r\n            </div>\r\n            <div className='p-4 space-y-2'>\r\n            {friends?.length > 0 ? (\r\n    friends\r\n        ?.filter(handleSearch) // تصفية حسب البحث\r\n        ?.filter(unreadMessagesContent) // تصفية الرسائل غير المقروءة\r\n        .map((friend) => (\r\n            <MessageItem\r\n                key={friend._id}\r\n                sender={friend.username}\r\n                profilePicture={friend.profilePicture}\r\n                id={friend._id}\r\n            />\r\n        ))\r\n) : (\r\n    <div className=\"text-center text-gray-500\">\r\n        <p>No Frindes found.</p>\r\n    </div>\r\n)}\r\n            </div>\r\n        </div>\r\n    );\r\n}"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,gBAAgB,CAAC,CAAC;AAC3C,SAASC,QAAQ,QAAQ,iBAAiB,CAAC,CAAC;AAC5C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,OAAO,MAAM,oBAAoB,CAAC,CAAC;AAC1C,SAASC,mBAAmB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,eAAe,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,eAAA;EAC9B,MAAM;IAAEC,OAAO;IAAGC,QAAQ;IAAGC;EAAK,CAAC,GAAGZ,QAAQ,CAAC,CAAC;EAEhD,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACa,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACe,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAInE,MAAMiB,YAAY,GAAIC,KAAK,IAAK;IAC7B,MAAMC,QAAQ,GAAG,GAAGD,KAAK,CAACE,QAAQ,GAAG;IACrC,OAAQD,QAAQ,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACT,MAAM,CAACQ,WAAW,CAAC,CAAC,CAAC;EAChE,CAAC;EAGD,IAAIV,WAAW,EAAE;IACjB,oBAAOP,OAAA,CAACH,OAAO;MACfsB,OAAO,EAAEA,CAAA,KAAMX,cAAc,CAAC,KAAK;IAAE;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACxC;EAEA,MAAMC,qBAAqB,GAAGC,OAAO,IAAI;IACrC,IAAG,CAACd,kBAAkB,EAAE,OAAO,IAAI;IAEnC,MAAMe,eAAe,GAAG5B,mBAAmB,CAAC6B,QAAQ,EAAGF,OAAO,CAACG,GAAG,CAAC;IAEnE,MAAMC,qBAAqB,GAAGH,eAAe,CAACI,IAAI,CAAEC,OAAO,IAAK,CAACA,OAAO,CAACC,IAAK,CAAC;IAE/E,OAAOH,qBAAqB;EAChC,CAAC;EAED,oBACI7B,OAAA;IAAKiC,SAAS,EAAC,uCAAuC;IAAAC,QAAA,gBAClDlC,OAAA;MAAKiC,SAAS,EAAC,gEAAgE;MAAAC,QAAA,gBAC3ElC,OAAA;QAAKiC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxClC,OAAA;UAAKmC,GAAG,EAAC,oDAAoD;UAC5DC,GAAG,EAAC,QAAQ;UACZH,SAAS,EAAC,qCAAqC;UAC/CI,OAAO,EAAEA,CAAA,KAAM7B,cAAc,CAAC,IAAI;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEtC,CAAC,eACNvB,OAAA;QAAKiC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eACxClC,OAAA;UAAGiC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EAC3C5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEU;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eACNvB,OAAA;MAAKiC,SAAS,EAAC,oDACM;MAAAC,QAAA,gBACjBlC,OAAA;QAAKiC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BlC,OAAA;UAAOsC,IAAI,EAAC,MAAM;UAACC,WAAW,EAAC,QAAQ;UAAEzB,KAAK,EAAEL,MAAO;UAAC+B,QAAQ,EAAEC,CAAC,IAAI/B,SAAS,CAAC+B,CAAC,CAACC,MAAM,CAAC5B,KAAK,CAAE;UAACmB,SAAS,EAAC;QAA4G;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC5NvB,OAAA,CAACR,QAAQ;UAACyC,SAAS,EAAC;QAAsC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNvB,OAAA;QAAQqC,OAAO,EAAEA,CAAA,KAAM;UAACzB,qBAAqB,CAAE+B,GAAG,IAAK,CAACA,GAAG,CAAC;QAAA,CAAE;QAAAT,QAAA,eAC1DlC,OAAA,CAACP,QAAQ;UAACwC,SAAS,EAAC,cAAc;UAACW,IAAI,EAAE;QAAG;UAAAxB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eACNvB,OAAA;MAAKiC,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC7B,CAAA9B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEyC,MAAM,IAAG,CAAC,GAC5BzC,OAAO,aAAPA,OAAO,wBAAAD,eAAA,GAAPC,OAAO,CACD0C,MAAM,CAACjC,YAAY,CAAC,CAAC;MAAA,cAAAV,eAAA,uBAD3BA,eAAA,CAEM2C,MAAM,CAACtB,qBAAqB,CAAC,CAAC;MAAA,CAC/BuB,GAAG,CAAEC,MAAM,iBACRhD,OAAA,CAACL,WAAW;QAERsD,MAAM,EAAED,MAAM,CAAChC,QAAS;QACxBkC,cAAc,EAAEF,MAAM,CAACE,cAAe;QACtCC,EAAE,EAAEH,MAAM,CAACpB;MAAI,GAHVoB,MAAM,CAACpB,GAAG;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAIlB,CACJ,CAAC,gBAENvB,OAAA;QAAKiC,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eACtClC,OAAA;UAAAkC,QAAA,EAAG;QAAiB;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IACR;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACgB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAACrB,EAAA,CA7EuBD,OAAO;EAAA,QACWP,QAAQ;AAAA;AAAA0D,EAAA,GAD1BnD,OAAO;AAAA,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}