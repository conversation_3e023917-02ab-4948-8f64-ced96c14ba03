import { createNativeStackNavigator } from "@react-navigation/native-stack";
import React, { useState } from "react";
import {
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import {
  Box,
  Text,
  Button,
  Link,
  Input,
  FormControl,
  VStack,
  Heading,
  Toast,
  HStack,
} from "native-base";
import { useNavigation } from "@react-navigation/native";
import * as Yup from "yup";
import { useFormik } from "formik";
import axios from "../libs/Api";
import { useStore } from "../libs/state";

const TopTab = createNativeStackNavigator();

function Register() {
  const [isLoading, setIsLoading] = useState(false);
  const navigation = useNavigation();
  const { setUser, setToken } = useStore();

  const formik = useFormik({
    initialValues: {
      name: "",
      email: "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      password: Yup.string()
        .min(6, "Password must be at least 6 characters")
        .required("Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("password"), null], "Passwords must match")
        .required("Confirm password is required"),
      name: Yup.string().required("Name is required"),
    }),
    onSubmit: async (values) => {
      setIsLoading(true);
      try {
        const response = await axios.post("/user/register", {
          email: values.email,
          password: values.password,
          name: values.name,
        });

        Toast.show({
          title: response.data.message || "Registration successful",
          status: response.data.errors ? "error" : "success",
          placement: "top",
        });

        if (!response.data.errors) {
          setUser(response.data.user);
          setToken(response.data.token);
          navigation.navigate("Home");
        }
      } catch (error) {
        Toast.show({
          title: error.response?.data?.message || "Something went wrong!",
          status: "error",
          placement: "top",
        });
      } finally {
        setIsLoading(false);
      }
    },
  });

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <Box safeArea w="90%" maxW={290}>
        <Image
          style={styles.logo}
          source={require("../assets/logo-hsoub.png")}
        />
        <Heading size="lg" fontWeight={600} color={"coolGray.800"}>
          Welcome
        </Heading>
        <Heading size="xs" mt="1" fontWeight="medium" color={"coolGray.600"}>
          Register to continue!
        </Heading>
        <VStack space={3} mt="5" w="100%">
          <FormControl isInvalid={formik.touched.name && formik.errors.name}>
            <FormControl.Label>Name</FormControl.Label>
            <Input
              placeholder="Enter your name"
              onChangeText={formik.handleChange("name")}
              value={formik.values.name}
              onBlur={formik.handleBlur("name")}
            />
            {formik.touched.name && formik.errors.name && (
              <Text color="red.500">{formik.errors.name}</Text>
            )}
          </FormControl>

          <FormControl isInvalid={formik.touched.email && formik.errors.email}>
            <FormControl.Label>Email Address</FormControl.Label>
            <Input
              placeholder="Enter your email address"
              onChangeText={formik.handleChange("email")}
              value={formik.values.email}
              onBlur={formik.handleBlur("email")}
            />
            {formik.touched.email && formik.errors.email && (
              <Text color="red.500">{formik.errors.email}</Text>
            )}
          </FormControl>

          <FormControl
            isInvalid={formik.touched.password && formik.errors.password}
          >
            <FormControl.Label>Password</FormControl.Label>
            <Input
              placeholder="Enter your password"
              value={formik.values.password}
              onChangeText={formik.handleChange("password")}
              secureTextEntry={true}
              onBlur={formik.handleBlur("password")}
            />
            {formik.touched.password && formik.errors.password && (
              <Text color="red.500">{formik.errors.password}</Text>
            )}
          </FormControl>

          <FormControl
            isInvalid={
              formik.touched.confirmPassword && formik.errors.confirmPassword
            }
          >
            <FormControl.Label>Confirm Password</FormControl.Label>
            <Input
              placeholder="Re-enter your password"
              value={formik.values.confirmPassword}
              onChangeText={formik.handleChange("confirmPassword")}
              secureTextEntry={true}
              onBlur={formik.handleBlur("confirmPassword")}
            />
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <Text color="red.500">{formik.errors.confirmPassword}</Text>
              )}
          </FormControl>

          <Button
            mt="2"
            colorScheme="indigo"
            onPress={formik.handleSubmit}
            isLoading={isLoading}
          >
            Register
          </Button>

          <HStack mt="6" justifyContent="center">
            <Text fontSize="sm" color="coolGray.600">
              I'm already registered.{" "}
            </Text>
            <Link
              _text={{
                color: "indigo.500",
                fontWeight: "medium",
                fontSize: "sm",
              }}
              onPress={() => navigation.navigate("Login")}
            >
              Login here.
            </Link>
          </HStack>
        </VStack>
      </Box>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f9fa",
  },
  logo: {
    width: 100,
    height: 100,
    marginBottom: 20,
    alignSelf: "center",
  },
});

export default Register;
