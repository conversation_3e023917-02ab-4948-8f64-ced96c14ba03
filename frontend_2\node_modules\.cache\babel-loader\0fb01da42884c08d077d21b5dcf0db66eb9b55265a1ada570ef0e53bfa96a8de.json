{"ast": null, "code": "var _jsxFileName = \"D:\\\\my Programing\\\\whatsapp-clone-2\\\\frontend_2\\\\src\\\\pages\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport io from \"socket.io-client\"; // Importing socket.io-client for real-time communication\nimport { useEffect, useCallback } from \"react\";\nimport Sidebar from \"../components/Sidebar/Sidebar\";\nimport { Outlet } from \"react-router-dom\";\nimport { useStore } from \"../libs/state\";\nimport { getMessages, getUsers } from \"../libs/requests\";\nimport axios from \"axios\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function Home() {\n  _s();\n  const {\n    addMessage,\n    setFriends,\n    setSocket,\n    setMessages,\n    setUser,\n    updateFriend,\n    setTyping,\n    addFriend,\n    setCurrentReceiver,\n    user,\n    accessToken,\n    currentReceiver,\n    messages,\n    friends\n  } = useStore();\n\n  // Fetching users and messages from API\n\n  const fetchData = async () => {\n    try {\n      const [userRes, messagRes] = await Promise.all([axios.get(\"http://localhost:3000/user\", {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      }), axios.get(\"http://localhost:3000/message\", {\n        headers: {\n          Authorization: `Bearer ${accessToken}`\n        }\n      })]);\n      console.log(\"User data:\", userRes.data);\n      console.log(\"Messages data:\", messagRes.data);\n      setFriends(userRes.data);\n      setMessages(messagRes.data);\n    } catch (error) {\n      var _error$response;\n      console.error(\"Error fetching data:\", ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n    }\n  };\n\n  // const fetchData = useCallback(async () => {\n  //   try {\n  //     const usersRes = await axios.get(\"http://localhost:3000/user\", {\n  //       headers: {\n  //         Authorization: token,\n  //       },\n  //     });\n  //     const users = usersRes.data;\n\n  //     const messagesRes = await axios.get(\"http://localhost:3000/message\", {\n  //       headers: {\n  //         Authorization: token,\n  //       },\n  //     });\n  //     const messages = messagesRes.data;\n\n  //     // Update your states here, for example:\n  //     // setFriends(users);\n  //     // setMessages(messages);\n\n  //   } catch (error) {\n  //     console.error(\"Error fetching data:\", error);\n  //   }\n  // }, [token]);  // Include the necessary dependencie\n  useEffect(() => {\n    if (!accessToken) {\n      console.error(\"Access token is missing. Cannot connect to the server.\");\n      return;\n    }\n    const socket = io('http://localhost:3000', {\n      query: {\n        token: accessToken\n      },\n      // 🔥 تأكد من تمرير التوكن ككائن وليس كسلسلة\n      transports: [\"websocket\"]\n    });\n    socket.on(\"connect\", () => {\n      console.log(`Connected to the server with the id: ${socket.id}`);\n      setSocket(socket);\n    });\n    socket.on(\"disconnect\", () => {\n      console.log(\"Disconnected from the server\");\n      setSocket(null);\n    });\n    socket.on(\"typing\", () => setTyping(true));\n    socket.on(\"stop_typing\", () => setTyping(false));\n    return () => {\n      socket.disconnect();\n    };\n  }, [accessToken, setSocket]);\n\n  // Fetch data when component mounts\n  useEffect(() => {\n    if (accessToken) {\n      fetchData();\n    }\n  }, [accessToken]);\n\n  // Log updated messages for debugging\n  useEffect(() => {\n    console.log(\"Messages updated:\", messages);\n  }, [messages]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"EQNgxAYUooCgw9jmLIev/7MfpsY=\", false, function () {\n  return [useStore];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["io", "useEffect", "useCallback", "Sidebar", "Outlet", "useStore", "getMessages", "getUsers", "axios", "jsxDEV", "_jsxDEV", "Home", "_s", "addMessage", "setFriends", "setSocket", "setMessages", "setUser", "updateFriend", "setTyping", "addFriend", "setCurrentReceiver", "user", "accessToken", "currentReceiver", "messages", "friends", "fetchData", "userRes", "messagRes", "Promise", "all", "get", "headers", "Authorization", "console", "log", "data", "error", "_error$response", "response", "message", "socket", "query", "token", "transports", "on", "id", "disconnect", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/my Programing/whatsapp-clone-2/frontend_2/src/pages/index.jsx"], "sourcesContent": ["import io from \"socket.io-client\"; // Importing socket.io-client for real-time communication\r\nimport { useEffect, useCallback } from \"react\";\r\nimport Sidebar from \"../components/Sidebar/Sidebar\";\r\nimport { Outlet } from \"react-router-dom\";\r\nimport { useStore } from \"../libs/state\";\r\nimport { getMessages, getUsers } from \"../libs/requests\";\r\nimport axios from \"axios\";\r\n\r\nexport default function Home() {\r\n  const {\r\n    addMessage,\r\n    setFriends,\r\n    setSocket,\r\n    setMessages,\r\n    setUser,\r\n    updateFriend,\r\n    setTyping,\r\n    addFriend,\r\n    setCurrentReceiver,\r\n    user,\r\n    accessToken,\r\n    currentReceiver,\r\n    messages,\r\n    friends\r\n  } = useStore();\r\n\r\n  // Fetching users and messages from API\r\n\r\n  const fetchData = async () => {\r\n    try {\r\n      const [userRes, messagRes] = await Promise.all([\r\n        axios.get(\"http://localhost:3000/user\", {\r\n          headers: {\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n        }),\r\n        axios.get(\"http://localhost:3000/message\", {\r\n          headers: {\r\n            Authorization: `Bearer ${accessToken}`,\r\n          },\r\n        }),\r\n      ]);\r\n\r\n      console.log(\"User data:\", userRes.data);\r\n      console.log(\"Messages data:\", messagRes.data);\r\n\r\n      setFriends(userRes.data);\r\n      setMessages(messagRes.data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching data:\", error.response?.data || error.message);\r\n    }\r\n  };\r\n\r\n\r\n  // const fetchData = useCallback(async () => {\r\n  //   try {\r\n  //     const usersRes = await axios.get(\"http://localhost:3000/user\", {\r\n  //       headers: {\r\n  //         Authorization: token,\r\n  //       },\r\n  //     });\r\n  //     const users = usersRes.data;\r\n\r\n  //     const messagesRes = await axios.get(\"http://localhost:3000/message\", {\r\n  //       headers: {\r\n  //         Authorization: token,\r\n  //       },\r\n  //     });\r\n  //     const messages = messagesRes.data;\r\n\r\n  //     // Update your states here, for example:\r\n  //     // setFriends(users);\r\n  //     // setMessages(messages);\r\n\r\n  //   } catch (error) {\r\n  //     console.error(\"Error fetching data:\", error);\r\n  //   }\r\n  // }, [token]);  // Include the necessary dependencie\r\n  useEffect(() => {\r\n    if (!accessToken) {\r\n      console.error(\"Access token is missing. Cannot connect to the server.\");\r\n      return;\r\n    }\r\n\r\n    const socket = io('http://localhost:3000', {\r\n      query: { token: accessToken },  // 🔥 تأكد من تمرير التوكن ككائن وليس كسلسلة\r\n      transports: [\"websocket\"],\r\n    });\r\n\r\n    socket.on(\"connect\", () => {\r\n      console.log(`Connected to the server with the id: ${socket.id}`);\r\n      setSocket(socket);\r\n    });\r\n\r\n    socket.on(\"disconnect\", () => {\r\n      console.log(\"Disconnected from the server\");\r\n      setSocket(null);\r\n    });\r\n\r\n    socket.on(\"typing\",()=> setTyping(true));\r\n    socket.on(\"stop_typing\",()=> setTyping(false));\r\n\r\n\r\n    return () => {\r\n      socket.disconnect();\r\n    };\r\n\r\n  }, [accessToken, setSocket]);\r\n\r\n\r\n  // Fetch data when component mounts\r\n  useEffect(() => {\r\n    if (accessToken) {\r\n      fetchData();\r\n    }\r\n  }, [accessToken]);\r\n\r\n  // Log updated messages for debugging\r\n  useEffect(() => {\r\n    console.log(\"Messages updated:\", messages);\r\n  }, [messages]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen\">\r\n      <Sidebar />\r\n      <Outlet />\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,EAAE,MAAM,kBAAkB,CAAC,CAAC;AACnC,SAASC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,OAAOC,OAAO,MAAM,+BAA+B;AACnD,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,QAAQ,QAAQ,eAAe;AACxC,SAASC,WAAW,EAAEC,QAAQ,QAAQ,kBAAkB;AACxD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,eAAe,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM;IACJC,UAAU;IACVC,UAAU;IACVC,SAAS;IACTC,WAAW;IACXC,OAAO;IACPC,YAAY;IACZC,SAAS;IACTC,SAAS;IACTC,kBAAkB;IAClBC,IAAI;IACJC,WAAW;IACXC,eAAe;IACfC,QAAQ;IACRC;EACF,CAAC,GAAGrB,QAAQ,CAAC,CAAC;;EAEd;;EAEA,MAAMsB,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACF,MAAM,CAACC,OAAO,EAAEC,SAAS,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC7CvB,KAAK,CAACwB,GAAG,CAAC,4BAA4B,EAAE;QACtCC,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUX,WAAW;QACtC;MACF,CAAC,CAAC,EACFf,KAAK,CAACwB,GAAG,CAAC,+BAA+B,EAAE;QACzCC,OAAO,EAAE;UACPC,aAAa,EAAE,UAAUX,WAAW;QACtC;MACF,CAAC,CAAC,CACH,CAAC;MAEFY,OAAO,CAACC,GAAG,CAAC,YAAY,EAAER,OAAO,CAACS,IAAI,CAAC;MACvCF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEP,SAAS,CAACQ,IAAI,CAAC;MAE7CvB,UAAU,CAACc,OAAO,CAACS,IAAI,CAAC;MACxBrB,WAAW,CAACa,SAAS,CAACQ,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA;MACdJ,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAE,EAAAC,eAAA,GAAAD,KAAK,CAACE,QAAQ,cAAAD,eAAA,uBAAdA,eAAA,CAAgBF,IAAI,KAAIC,KAAK,CAACG,OAAO,CAAC;IAC9E;EACF,CAAC;;EAGD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACAxC,SAAS,CAAC,MAAM;IACd,IAAI,CAACsB,WAAW,EAAE;MAChBY,OAAO,CAACG,KAAK,CAAC,wDAAwD,CAAC;MACvE;IACF;IAEA,MAAMI,MAAM,GAAG1C,EAAE,CAAC,uBAAuB,EAAE;MACzC2C,KAAK,EAAE;QAAEC,KAAK,EAAErB;MAAY,CAAC;MAAG;MAChCsB,UAAU,EAAE,CAAC,WAAW;IAC1B,CAAC,CAAC;IAEFH,MAAM,CAACI,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBX,OAAO,CAACC,GAAG,CAAC,wCAAwCM,MAAM,CAACK,EAAE,EAAE,CAAC;MAChEhC,SAAS,CAAC2B,MAAM,CAAC;IACnB,CAAC,CAAC;IAEFA,MAAM,CAACI,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5BX,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3CrB,SAAS,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IAEF2B,MAAM,CAACI,EAAE,CAAC,QAAQ,EAAC,MAAK3B,SAAS,CAAC,IAAI,CAAC,CAAC;IACxCuB,MAAM,CAACI,EAAE,CAAC,aAAa,EAAC,MAAK3B,SAAS,CAAC,KAAK,CAAC,CAAC;IAG9C,OAAO,MAAM;MACXuB,MAAM,CAACM,UAAU,CAAC,CAAC;IACrB,CAAC;EAEH,CAAC,EAAE,CAACzB,WAAW,EAAER,SAAS,CAAC,CAAC;;EAG5B;EACAd,SAAS,CAAC,MAAM;IACd,IAAIsB,WAAW,EAAE;MACfI,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACJ,WAAW,CAAC,CAAC;;EAEjB;EACAtB,SAAS,CAAC,MAAM;IACdkC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEX,QAAQ,CAAC;EAC5C,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACEf,OAAA;IAAKuC,SAAS,EAAC,eAAe;IAAAC,QAAA,gBAC5BxC,OAAA,CAACP,OAAO;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACX5C,OAAA,CAACN,MAAM;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAAC1C,EAAA,CAxHuBD,IAAI;EAAA,QAgBtBN,QAAQ;AAAA;AAAAkD,EAAA,GAhBU5C,IAAI;AAAA,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}