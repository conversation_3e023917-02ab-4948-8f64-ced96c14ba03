import mongoose from "mongoose";


const messageSchema = new mongoose.Schema({
    content: { type: String, required: true},
    seen: { type: Boolean, default: false},
    senderId : {type: mongoose.Schema.Types.ObjectId, required: true ,
        // ref is a property that refrences the name of the model
        ref: "User"},
        receiverId  : {type: mongoose.Schema.Types.ObjectId, required: true ,
        // ref is a property that refrences the name of the model
        ref: "User"},

}, // timestamps is used to create createdAt and updatedAt
 { timestamps: true });

 const Message = mongoose.model("Message", messageSchema);

export default Message;