{"name": "application", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "axios": "^1.7.9", "expo": "~52.0.24", "expo-blur": "~14.0.1", "expo-constants": "~17.0.3", "expo-font": "~13.0.2", "expo-haptics": "~14.0.0", "expo-linking": "~7.0.3", "expo-router": "~4.0.16", "expo-splash-screen": "~0.29.19", "expo-status-bar": "~2.0.0", "expo-symbols": "~0.2.0", "expo-system-ui": "~4.0.6", "expo-web-browser": "~14.0.1", "native-base": "^3.4.28", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "0.76.5", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5", "zustand": "^5.0.3", "babel": {"presets": ["module:metro-react-native-babel-preset"], "plugins": [["module:react-native-dotenv", {"moduleName": "@env", "path": ".env"}]]}}, "devDependencies": {"@babel/core": "^7.25.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "jest": "^29.2.1", "jest-expo": "~52.0.2", "react-test-renderer": "18.3.1", "typescript": "^5.3.3", "eslint-config-expo": "~8.0.1"}, "private": true}