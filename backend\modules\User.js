import mongoose from "mongoose";

  
const userSchema = new mongoose.Schema({
    // lastname: { type: String, required: true,  maxLength: 30 },
    // firstname: { type: String, required: true,  maxLength: 30 },
    username: { type: String, required: true,  },
    email: { type: String, required: true, unique: true },
    password: { type: String, required: true },
    state: { type: String, default: "active" || "" },
    profilePicture: { type: String, required: false , default: "" },
    PhoneNumber: { type: String, required: false , default: "" },

}, // timestamps is used to create createdAt and updatedAt
 { timestamps: true });

 const User = mongoose.model("User", userSchema); 

export default User;





