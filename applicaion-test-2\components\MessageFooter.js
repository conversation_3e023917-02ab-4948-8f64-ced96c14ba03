import React, { useState } from 'react';
import { StyleSheet, TextInput, View, KeyboardAvoidingView } from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import { useStore } from '../libs/state';

export default function MessageFooter() {
  const [input, setInput] = useState(''); // حفظ النص المدخل
  const { soket } = useStore(); // جلب الـ socket من الحالة العامة

  const sendMessage = () => {
    if (!soket) {
      console.error("Socket is not initialized."); // التحقق من وجود الـ socket
      return;
    }
    if (input.trim()) {
      // إرسال الرسالة عبر الـ socket
      soket.emit('send_message', {
        receiverId: '123', // يمكنك استبدال المعرف بمعرف ديناميكي
        content: input.trim(),
      });
      setInput(''); // إعادة تعيين النص المدخل
    }

    if(input.trim()) {
      soket.emit('send_message', {
        receiverId: '645ba3c1abe5e54e9092c87a', // يمكنك استبدال المعرف بمعرف ديناميكي
        content: input.trim(),
      });
      setInput(''); // إعادة تعيين النص المدخل
    }
  };

  return (
    <KeyboardAvoidingView behavior="padding">
      <View style={styles.container}>
        {/* إدخال النص */}
        <TextInput
          placeholder="Type a message"
          style={styles.input}
          value={input}
          onChangeText={setInput} // تحديث النص عند الكتابة
        />
        {/* زر الإرسال */}
        <Icon
          name="send"
          size={24}
          color="white"
          style={styles.icon}
          onPress={sendMessage}
        />
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    backgroundColor: '#f2f2f2',
  },
  input: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 10,
    marginRight: 8,
    fontSize: 16,
  },
  icon: {
    padding: 8,
    backgroundColor: '#1b9c85',
    borderRadius: 50,
  },
});
