{"name": "application51", "version": "1.0.0", "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~3.2.3", "expo": "~51.0.28", "expo-status-bar": "~1.12.1", "react": "18.2.0", "react-native": "0.74.5", "react-native-dotenv": "^3.4.11"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.26.3"}, "private": true}