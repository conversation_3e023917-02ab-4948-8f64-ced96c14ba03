import User from "../modules/User.js";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import { io } from "../index.js";



export const register = async (req, res) => {
    try {
        const { username, email, password } = req.body;

        const existingUser = await User.findOne({ email });
        if (existingUser) {
            return res.status(400).json({ message: "User already exists" });
        }

        const saltRounds = 12;
        const hashPassword = await bcrypt.hash(password, saltRounds);

        const defaultPicture = "http://localhost:3000/upload/default.png"; 

        const user = await User.create({ 
            username, 
            email, 
            password: hashPassword, 
            profilePicture: defaultPicture,
            PhoneNumber: ""
        });

        user.password = undefined;
        
        if (!process.env.JWT_SECRET) {
            throw new Error("JWT secret not set");
        }

        const token = jwt.sign(
            { userId: user._id }, 
            process.env.JWT_SECRET, 
            { expiresIn: "10d" }
        );

        res.status(201).json({
            message: "User created successfully",
            user,
            token
        });

    } catch (error) {
        console.error("Error during user registration:", error);
        res.status(500).json({ message: "Server error occurred" });
    }
};


export const login = async (req, res) => {
    try{
        // استقبال البريد الإلكتروني وكلمة المرور
        const { email, password } = req.body;

        // التحقق من وجود المستخدم
        const user = await User.findOne({email});

        if (!user) {
            return res.json({ message: "User does not exist"});
            
        }

        // التحقق من كلمة المرور compare password
        const validPassword = await bcrypt.compare(password, user.password);

        if (!validPassword) {
            return res.json({ message: "User does not exist" });

            
        };

          // إزالة كلمة المرور من الاستجابة 
        user.password = undefined;
       
        // إنشاء التوكن
        const token = jwt.sign(
            { userId: user._id }, 
            process.env.JWT_SECRET, 
            { expiresIn: "10d" }
        );

        res.status(200).json(
            {
                message: "User logged in successfully",
                user,
                token
            }
        )
    }  catch (error) {
        console.error(error);
        res.status(500).json({ message: "Server error cant run" });
    }
}


export const getFriends = async (req, res) => {
    const users = await User.find({
        _id: { $ne: req.userId},

    }).select("-password");// لا تعرض كلمة المرور

    res.send(users);
};

export const updateUser = async (req, res , props) => {

    const {username ,PhoneNumber ,profilePicture} = req.body;
    const user = await User.findByIdAndUpdate(props.userId,
         {username, PhoneNumber , profilePicture}, 
         {new: true}); // new : true to return the updated user in the response
         (req.userId);

         io.emit("user_updated", user);
         res.send(user);
}

export const updateProfilePicture = async (req, res) => {
   const userId =req.userId;
   
   const profilePicture = 'http://localhost:3000/uploads/'+req.file.filename;

   const user = await User.findByIdAndUpdate(userId, {profilePicture}, {new: true});

   io.emit("user_updated", user)
   res.send(user);
}
