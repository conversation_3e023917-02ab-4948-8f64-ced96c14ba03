{"ast": null, "code": "var _jsxFileName = \"D:\\\\my Programing\\\\whatsapp-clone-2\\\\frontend_2\\\\src\\\\components\\\\Sidebar\\\\MessageItem.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport moment from 'moment';\nimport { useStore } from '../../libs/state';\nimport { useNavigate } from 'react-router-dom';\nimport defaultAvatar from '../../assets/logo-hsoub.png'; // صورة افتراضية\nimport { getReceiverMessages } from '../../libs/functions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function MessageItem(props) {\n  _s();\n  const navigate = useNavigate();\n  const {\n    currentReceiver,\n    setCurrentReceiver,\n    socket,\n    messages\n  } = useStore();\n\n  // الحصول على رسائل المستخدم الحالي\n  const contactMessages = getReceiverMessages(messages, props.id);\n\n  // حساب عدد الرسائل غير المقروءة\n  const unreadMessages = contactMessages === null || contactMessages === void 0 ? void 0 : contactMessages.filter(message => !message.seen && message.receiverId === props.id).length;\n\n  // الحصول على توقيت آخر رسالة\n  const lastMessageTime = contactMessages.length ? contactMessages[contactMessages.length - 1].createdAt : null;\n\n  // عند الضغط على العنصر\n  function onClick() {\n    var _props$sender;\n    navigate(`/${props.id}`);\n    setCurrentReceiver({\n      _id: props.id || 'unknown id',\n      name: ((_props$sender = props.sender) === null || _props$sender === void 0 ? void 0 : _props$sender.username) || 'Unknown User'\n    });\n    if (socket && typeof socket.emit === 'function') {\n      socket.emit('seen', props.id);\n      console.log('Socket emit: seen');\n    } else {\n      console.error('Socket is not initialized or emit is not a function.');\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    onClick: onClick,\n    className: \"flex items-center space-x-2 cursor-pointer hover:bg-gray-700 p-2 rounded-lg\",\n    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n      alt: \"profile-picture\",\n      src: props.profilePicture || defaultAvatar,\n      className: \"w-10 h-10 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-white font-semibold flex-1\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-white font-semibold\",\n        children: props.sender.username || 'Unknown User'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-400\",\n        children: contactMessages.length ? contactMessages[contactMessages.length - 1].content : 'Start of message'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-col items-end\",\n      children: [unreadMessages > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-4 h-4 rounded-full bg-red-500 text-white text-xs flex justify-center items-center\",\n        children: unreadMessages\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-gray-400\",\n        children: lastMessageTime ? moment(lastMessageTime).fromNow() : ''\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(MessageItem, \"wWoQd7MDNeThJg39bYHJ7+uiAo8=\", false, function () {\n  return [useNavigate, useStore];\n});\n_c = MessageItem;\nvar _c;\n$RefreshReg$(_c, \"MessageItem\");", "map": {"version": 3, "names": ["React", "moment", "useStore", "useNavigate", "defaultAvatar", "getReceiverMessages", "jsxDEV", "_jsxDEV", "MessageItem", "props", "_s", "navigate", "currentReceiver", "setCurrentReceiver", "socket", "messages", "contactMessages", "id", "unreadMessages", "filter", "message", "seen", "receiverId", "length", "lastMessageTime", "createdAt", "onClick", "_props$sender", "_id", "name", "sender", "username", "emit", "console", "log", "error", "className", "children", "alt", "src", "profilePicture", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "fromNow", "_c", "$RefreshReg$"], "sources": ["D:/my Programing/whatsapp-clone-2/frontend_2/src/components/Sidebar/MessageItem.jsx"], "sourcesContent": ["import React from 'react';\r\nimport moment from 'moment';\r\nimport { useStore } from '../../libs/state';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport defaultAvatar from '../../assets/logo-hsoub.png'; // صورة افتراضية\r\nimport { getReceiverMessages } from '../../libs/functions';\r\n\r\n\r\n\r\nexport default function MessageItem(props) {\r\n  const navigate = useNavigate();\r\n\r\n  const { currentReceiver, setCurrentReceiver, socket, messages } = useStore();\r\n\r\n  // الحصول على رسائل المستخدم الحالي\r\n  const contactMessages = getReceiverMessages(messages, props.id);\r\n\r\n  // حساب عدد الرسائل غير المقروءة\r\n  const unreadMessages = contactMessages?.filter(\r\n    (message) => !message.seen && message.receiverId === props.id\r\n  ).length;\r\n\r\n  // الحصول على توقيت آخر رسالة\r\n  const lastMessageTime = contactMessages.length\r\n    ? contactMessages[contactMessages.length - 1].createdAt\r\n    : null;\r\n\r\n  // عند الضغط على العنصر\r\n  function onClick() {\r\n    navigate(`/${props.id}`);\r\n\r\n    setCurrentReceiver({\r\n      _id: props.id || 'unknown id',\r\n      name: props.sender?.username || 'Unknown User',\r\n    });\r\n\r\n    if (socket && typeof socket.emit === 'function') {\r\n      socket.emit('seen', props.id);\r\n      console.log('Socket emit: seen');\r\n    } else {\r\n      console.error('Socket is not initialized or emit is not a function.');\r\n    }\r\n  }\r\n\r\n  return (\r\n    <div\r\n      onClick={onClick}\r\n      className=\"flex items-center space-x-2 cursor-pointer hover:bg-gray-700 p-2 rounded-lg\"\r\n    >\r\n      {/* صورة الملف الشخصي */}\r\n      <img\r\n        alt=\"profile-picture\"\r\n        src={props.profilePicture || defaultAvatar}\r\n        className=\"w-10 h-10 rounded-full\"\r\n      />\r\n\r\n      {/* معلومات المستخدم */}\r\n      <div className=\"text-white font-semibold flex-1\">\r\n        <p className=\"text-sm text-white font-semibold\">\r\n          {props.sender.username || 'Unknown User'}\r\n        </p>\r\n        <p className=\"text-xs text-gray-400\">\r\n          {contactMessages.length\r\n            ? contactMessages[contactMessages.length - 1].content\r\n            : 'Start of message'}\r\n        </p>\r\n      </div>\r\n\r\n      {/* عدد الرسائل غير المقروءة */}\r\n      <div className=\"flex flex-col items-end\">\r\n        {unreadMessages > 0 && (\r\n          <div className=\"w-4 h-4 rounded-full bg-red-500 text-white text-xs flex justify-center items-center\">\r\n            {unreadMessages}\r\n          </div>\r\n        )}\r\n        {/* توقيت آخر رسالة */}\r\n        <p className=\"text-xs text-gray-400\">\r\n          {lastMessageTime ? moment(lastMessageTime).fromNow() : ''}\r\n        </p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,aAAa,MAAM,6BAA6B,CAAC,CAAC;AACzD,SAASC,mBAAmB,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI3D,eAAe,SAASC,WAAWA,CAACC,KAAK,EAAE;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAE9B,MAAM;IAAES,eAAe;IAAEC,kBAAkB;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC;;EAE5E;EACA,MAAMc,eAAe,GAAGX,mBAAmB,CAACU,QAAQ,EAAEN,KAAK,CAACQ,EAAE,CAAC;;EAE/D;EACA,MAAMC,cAAc,GAAGF,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEG,MAAM,CAC3CC,OAAO,IAAK,CAACA,OAAO,CAACC,IAAI,IAAID,OAAO,CAACE,UAAU,KAAKb,KAAK,CAACQ,EAC7D,CAAC,CAACM,MAAM;;EAER;EACA,MAAMC,eAAe,GAAGR,eAAe,CAACO,MAAM,GAC1CP,eAAe,CAACA,eAAe,CAACO,MAAM,GAAG,CAAC,CAAC,CAACE,SAAS,GACrD,IAAI;;EAER;EACA,SAASC,OAAOA,CAAA,EAAG;IAAA,IAAAC,aAAA;IACjBhB,QAAQ,CAAC,IAAIF,KAAK,CAACQ,EAAE,EAAE,CAAC;IAExBJ,kBAAkB,CAAC;MACjBe,GAAG,EAAEnB,KAAK,CAACQ,EAAE,IAAI,YAAY;MAC7BY,IAAI,EAAE,EAAAF,aAAA,GAAAlB,KAAK,CAACqB,MAAM,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,QAAQ,KAAI;IAClC,CAAC,CAAC;IAEF,IAAIjB,MAAM,IAAI,OAAOA,MAAM,CAACkB,IAAI,KAAK,UAAU,EAAE;MAC/ClB,MAAM,CAACkB,IAAI,CAAC,MAAM,EAAEvB,KAAK,CAACQ,EAAE,CAAC;MAC7BgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;IAClC,CAAC,MAAM;MACLD,OAAO,CAACE,KAAK,CAAC,sDAAsD,CAAC;IACvE;EACF;EAEA,oBACE5B,OAAA;IACEmB,OAAO,EAAEA,OAAQ;IACjBU,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAGvF9B,OAAA;MACE+B,GAAG,EAAC,iBAAiB;MACrBC,GAAG,EAAE9B,KAAK,CAAC+B,cAAc,IAAIpC,aAAc;MAC3CgC,SAAS,EAAC;IAAwB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAGFrC,OAAA;MAAK6B,SAAS,EAAC,iCAAiC;MAAAC,QAAA,gBAC9C9B,OAAA;QAAG6B,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAC5C5B,KAAK,CAACqB,MAAM,CAACC,QAAQ,IAAI;MAAc;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC,CAAC,eACJrC,OAAA;QAAG6B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACjCrB,eAAe,CAACO,MAAM,GACnBP,eAAe,CAACA,eAAe,CAACO,MAAM,GAAG,CAAC,CAAC,CAACsB,OAAO,GACnD;MAAkB;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGNrC,OAAA;MAAK6B,SAAS,EAAC,yBAAyB;MAAAC,QAAA,GACrCnB,cAAc,GAAG,CAAC,iBACjBX,OAAA;QAAK6B,SAAS,EAAC,qFAAqF;QAAAC,QAAA,EACjGnB;MAAc;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CACN,eAEDrC,OAAA;QAAG6B,SAAS,EAAC,uBAAuB;QAAAC,QAAA,EACjCb,eAAe,GAAGvB,MAAM,CAACuB,eAAe,CAAC,CAACsB,OAAO,CAAC,CAAC,GAAG;MAAE;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAClC,EAAA,CAzEuBF,WAAW;EAAA,QAChBL,WAAW,EAEsCD,QAAQ;AAAA;AAAA6C,EAAA,GAHpDvC,WAAW;AAAA,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}