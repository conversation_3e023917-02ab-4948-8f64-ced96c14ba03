import { create } from 'zustand';

export const useStore = create((set) => ({
    user: null,
    token: '',
    // تسجيل الدخول
    setUser: (user) => set({ user }),
    setToken: (token) => {
        localStorage.setItem('token', token);
        return set({ token });
    },
    friends: [],
    setFriends: (friends) => set({ friends }),
    soket: null,
    setSoket: (soket)=> set({ soket}),
    Messages:[],
    setMessage: (Messages) => set({ Messages }),
    currentReceiver: null,
    setCurrentReceiver: (currentReceiver) => set({ currentReceiver }),
    typing : false,
    setTyping: (typing) => set({ typing }),
    input: null,
    setInput: (input) => set({ input }),
}));