import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons'; // الاستيراد الصحيح
import { useNavigation } from '@react-navigation/native';
import { useStore } from '../libs/state'; // تأكد من مسار الاستيراد الصحيح

export default function Header() {
  const navigation = useNavigation();
  const { user } = useStore(); // جلب بيانات المستخدم من الحالة

  return (
    <View style={styles.headerContainer}>
      {/* عرض اسم المستخدم إذا كان موجودًا */}
      <Text style={styles.Title}>
        {user?.name || 'Welcome'} 
      </Text>

      {/* زر تسجيل الخروج */}
      <MaterialIcons
        name="logout"
        style={styles.icon}
        size={24}
        onPress={() => {
          navigation.navigate('Login'); // الانتقال إلى صفحة تسجيل الدخول
        }}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#0e806a', // لون خلفية الهيدر
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
  },
  Title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white', // لون النص
  },
  icon: {
    color: 'white', // لون الأيقونة
  },
});
