import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {view , Text} from 'react-native'
import { Button, View } from 'react-native'
import { Image, StyleSheet } from 'react-native'
import { useNavigation } from '@react-navigation/native';


export default function Community() {
  const navigation = useNavigation();
  return (
    <View>
     
     <View style={{ flex : 1}}>
      <Image  sorce={require("../assets/logo-hsoub.png")}
       style={styles.image}
      />
        <Text style={styles.text}>Welcome to Chat App</Text>
        <Button title="Go to Login" onPress={() => navigation.navigate("Chat")} />
          <View style={{flex:1}} />
       </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: "center",
  },
  image: {
    transform: [{ scale: 0.75 }],
  },
  text: {
    fontSize: 32,
    marginVertical: 20,
  },
});