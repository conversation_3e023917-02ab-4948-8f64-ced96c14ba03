import React from 'react';
import { FaSearch } from 'react-icons/fa'; // import the search icon from react-icons/fa
import { IoFilter } from 'react-icons/io5'; // import the filter icon from react-icons/io5
import { useStore } from "../../libs/state";
import MessageItem from './MessageItem';
import { useState } from 'react';
import Profile from '../Profile/Profile'; // import the Profile component
import { getReceiverMessages } from '../../libs/functions';

export default function Sidebar() {
    const { friends , messages , user } = useStore();

    const [showProfile, setShowProfile] = useState(false);
    const [search, setSearch] = useState('');
    const [showUnseenMessages, setShowUnseenMessages] = useState(false);



    const handleSearch = (value) => {
       const fullName = `${value.username} `;
       return  fullName.toLowerCase().includes(search.toLowerCase());
    }


    if (showProfile) {
    return <Profile
    onClose={() => setShowProfile(false)} />;
    }

    const unreadMessagesContent = contact => {
        if(!showUnseenMessages) return true;

        const contactMessages = getReceiverMessages(messages , contact._id);

        const containUnseenMessages = contactMessages.some((message) => !message.seen );

        return containUnseenMessages;
    }

    return (
        <div className='flex-none w-72 bg-gray-800 text-white'>
            <div className='flex items-center justify-between p-4 border-b border-gray-700'>
                <div className='flex items-center space-x-2'>
                    <img src='https://img.icons8.com/ios_filled/512/whatsapp.png'
                     alt='avatar'
                     className='w-8 h-8 rounded-full cursor-pointer'
                     onClick={() => setShowProfile(true)}/>

                </div>
                <div className='flex items-center space-x-2'>
                    <p className='cursor-pointer text-white text-sm'>
                        {user?.username}
                    </p>
                </div>
            </div>
            <div className='p-4 flex justify-between it
            ems-center space-x-3'>
                <div className='relative flex-1'>
                    <input type='text' placeholder='search'  value={search} onChange={e => setSearch(e.target.value)} className='w-full bg-gray-700 text-white rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-opacity-50'  />
                    <FaSearch className='absolute top-2 right-2 text-gray-500' />
                </div>
                <button onClick={() => {setShowUnseenMessages((val) => !val)}}>
                    <IoFilter className='text-red-500' size={20} />
                </button>
            </div>
            <div className='p-4 space-y-2'>
            {friends?.length > 0 ? (
    friends
        ?.filter(handleSearch) // تصفية حسب البحث
        ?.filter(unreadMessagesContent) // تصفية الرسائل غير المقروءة
        .map((friend) => (
            <MessageItem
                key={friend._id}
                sender={friend}
                profilePicture={friend.profilePicture}
                id={friend._id}
            />
        ))
) : (
    <div className="text-center text-gray-500">
        <p>No Frindes found.</p>
    </div>
)}
            </div>
        </div>
    );
}