import React, { useState } from 'react';
import { View, Text, StyleSheet, Image, TouchableOpacity, KeyboardAvoidingView, Button, Alert, ActivityIndicator } from 'react-native';
import EditUserMode from '../components/EditUserMode';
import { useStore } from '../libs/state';
import * as ImagePicker from 'expo-image-picker'; // تأكد من استخدام `* as` لاستيراد `ImagePicker`

export default function Profile() {
  const { user, setToken } = useStore();
  const { username, email, status, ProfilePicture } = user || {}; // إضافة فحص إذا كان `user` فارغًا
  const actualStatus = status || "No status";

  // useState
  const [file, setFile] = useState(ProfilePicture);
  const [modalVisible, setModalVisible] = useState(false);

  // عرض مؤشر إذا لم يتم تحميل المستخدم
  if (!user) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0e806a" />
        <Text>Loading user data...</Text>
      </View>
    );
  }

  function openModal() {
    setModalVisible(true);
  }

  function closeModal() {
    setModalVisible(false);
  }

  async function pickImage() {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permission Required', 'Sorry, we need camera roll permissions to proceed.');
      return;
    }

    // فتح مكتبة الصور
    const result = await ImagePicker.launchImageLibraryAsync();
    if (!result.canceled) {
      const localUri = result.assets[0].uri;
      setFile(localUri);
    }
  }

  return (
    <KeyboardAvoidingView style={styles.container}>
      <EditUserMode modalVisible={modalVisible} closeModal={closeModal} />
      <View style={styles.imageContainer}>
        <TouchableOpacity onPress={pickImage}>
          <Image
            style={styles.profilePicture}
            source={file ? { uri: file } : require('../assets/logo-hsoub.png')} // صورة افتراضية في حال عدم وجود صورة
          />
        </TouchableOpacity>
      </View>
      <View style={styles.subContainer}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>Your Name</Text>
          <Text style={styles.label}>Status</Text>
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.text}>{username || "No name"}</Text>
          <Text style={styles.text}>{actualStatus}</Text>
        </View>
      </View>
      <View style={styles.buttonContainer}>
        <Button
          title="Edit Profile"
          onPress={openModal}
          color="#0e806a"
        />
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginTop: 50,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageContainer: {
    alignItems: 'center',
  },
  profilePicture: {
    width: 150,
    height: 150,
    borderRadius: 75,
    backgroundColor: '#ccc', // خلفية افتراضية
  },
  subContainer: {
    marginTop: 40,
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  labelContainer: {
    flex: 1,
  },
  textContainer: {
    flex: 1,
  },
  text: {
    fontSize: 20,
    fontWeight: 'bold',
    marginVertical: 10,
  },
  label: {
    fontSize: 20,
    marginVertical: 10,
  },
  buttonContainer: {
    marginTop: 20,
    paddingHorizontal: 20,
  },
});
