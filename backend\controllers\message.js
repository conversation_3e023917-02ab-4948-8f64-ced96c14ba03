import Message from "../modules/Message.js";

export const getMessages = async (req, res) => {
  try {
    const senderId = req.userId; // المستخدم الحالي
    const receiverId  = req.params.id; // المستخدم الذي يتم التواصل معه

    // البحث عن جميع الرسائل بين المستخدمين
    const messages = await Message.find({
      $or: [
        { senderId: senderId, receiverId : receiverId  }, // رسائل أرسلها المستخدم الحالي
        { senderId: receiverId , receiverId : senderId } // رسائل استقبلها المستخدم الحالي
      ]
    });

    res.send(messages);
  } catch (error) {
    console.error("Error fetching messages:", error);
    res.status(500).send({ message: "Failed to fetch messages." });
  }
};
 