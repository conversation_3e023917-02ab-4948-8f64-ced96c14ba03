import React from 'react';
import moment from 'moment';
import { useStore } from '../../libs/state';
import { useNavigate } from 'react-router-dom';
import defaultAvatar from '../../assets/logo-hsoub.png'; // صورة افتراضية
import { getReceiverMessages } from '../../libs/functions';



export default function MessageItem(props) {
  const navigate = useNavigate();

  const { currentReceiver, setCurrentReceiver, socket, messages } = useStore();

  // الحصول على رسائل المستخدم الحالي
  const contactMessages = getReceiverMessages(messages, props.id);

  // حساب عدد الرسائل غير المقروءة
  const unreadMessages = contactMessages?.filter(
    (message) => !message.seen && message.receiverId === props.id
  ).length;

  // الحصول على توقيت آخر رسالة
  const lastMessageTime = contactMessages.length
    ? contactMessages[contactMessages.length - 1].createdAt
    : null;

  // عند الضغط على العنصر
  function onClick() {
    navigate(`/${props.id}`);

    setCurrentReceiver({
      _id: props.id || 'unknown id',
      name: props.sender?.username || 'Unknown User',
    });

    if (socket && typeof socket.emit === 'function') {
      socket.emit('seen', props.id);
      console.log('Socket emit: seen');
    } else {
      console.error('Socket is not initialized or emit is not a function.');
    }
  }

  return (
    <div
      onClick={onClick}
      className="flex items-center space-x-2 cursor-pointer hover:bg-gray-700 p-2 rounded-lg"
    >
      {/* صورة الملف الشخصي */}
      <img
        alt="profile-picture"
        src={props.profilePicture || defaultAvatar}
        className="w-10 h-10 rounded-full"
      />

      {/* معلومات المستخدم */}
      <div className="text-white font-semibold flex-1">
        <p className="text-sm text-white font-semibold">
          {props.sender.username || 'Unknown User'}
        </p>
        <p className="text-xs text-gray-400">
          {contactMessages.length
            ? contactMessages[contactMessages.length - 1].content
            : 'Start of message'}
        </p>
      </div>

      {/* عدد الرسائل غير المقروءة */}
      <div className="flex flex-col items-end">
        {unreadMessages > 0 && (
          <div className="w-4 h-4 rounded-full bg-red-500 text-white text-xs flex justify-center items-center">
            {unreadMessages}
          </div>
        )}
        {/* توقيت آخر رسالة */}
        <p className="text-xs text-gray-400">
          {lastMessageTime ? moment(lastMessageTime).fromNow() : ''}
        </p>
      </div>
    </div>
  );
}
