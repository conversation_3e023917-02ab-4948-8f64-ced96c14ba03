import React from 'react'
import { View , Text} from 'react-native'
import { useRoute } from '@react-navigation/native' 
import { getReceiverMessages } from '../libs/function';
import { useStore } from '../libs/state';
import { useNavigation } from '@react-navigation/native';
import { FlatList } from 'react-native';
import MessageItem from '../components/MessageItem';
import MessageFooter from '../components/MessageFooter';
import { useEffect } from 'react';


export default function Messages() {
    const routes = useRoute();
    const { messages , user} = useStore();
    const navigation = useNavigation();

    const { _id , name } = routes.params;

   useEffect(()=> {
    navigation.setOptions({ title : name})

   }, [])

    const  contactMessages = getReceiverMessages(messages, _id);

  return (
    <View>
      <FlatList
       data={contactMessages} 
       keyExtractor={(item, i) => i.toString()} // استخدم index كمعرف لكل عنصر
       renderItem={({item}) =><MessageItem content={item.content} createdAt={item.createdAt} isSender={item.senderId === user._id} />}
        />

        <MessageFooter _id={_id} />
    </View>
  )
}
