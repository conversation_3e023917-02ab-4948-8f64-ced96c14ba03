import React from 'react';
import { View, Text, Image, TouchableOpacity, StyleSheet } from 'react-native';
import moment from 'moment';
import { useStore } from '../libs/state';
import { getReceiverMessages } from '../libs/function';
import { useNavigation } from '@react-navigation/native';


export default function ChatItem(props) {
  const navigation = useNavigation();
  const { Messages, soket } = useStore();
  const { _id, user, profilePicture } = props;


  const contactMessages = getReceiverMessages(Messages, props._id);
  const lastMessage = contactMessages[contactMessages.length - 1];

  const unreadMessages = contactMessages?.filter((message) => 
    !message.seen && message.receiverId === props._id
  ).length || 0;

  return (
    <TouchableOpacity
    onPress={() => {
      soket?.emit("seen", _id);
      navigation.navigate("Messages", {
        _id,
        user,
        profilePicture,
      });
    }}
  >
      <View style={styles.container}>
        <View style={styles.chatContainer}>
          <Image style={styles.image} source={{ uri: props.profilePicture }} />
          <View style={styles.chatContent}>
            <Text style={styles.name}>{props.name}</Text>
            <Text style={styles.lastMessage}>
              {lastMessage?.message || 'Start the conversation'}
            </Text>
          </View>
        </View>
        <View style={styles.unreadMessageContainer}>
          <Text style={styles.time}>
            {moment(props.createdAt).format('hh:mm A')}
          </Text>
          {unreadMessages > 0 && (
            <View style={styles.unreadMessages}>
              <Text style={{ color: 'white' }}>
                {unreadMessages < 10 ? unreadMessages : '9+'}
              </Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomColor: "#e0e0e0",
    borderBottomWidth: 1,
  },
  chatContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 50,
  },
  chatContent: {
    marginLeft: 16,
  },
  unreadMessageContainer: {
    flexDirection: "column",
    alignItems: "flex-end",
    flex: 1,
  },
  time: {
    color: "#9e9e9e",
  },
  unreadMessages: {
    backgroundColor: "#0e806a",
    width: 25,
    height: 25,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 4,
  },
  lastMessage: {
    color: "#9e9e9e",
    width: 200,
    overflow: "hidden",
  },
  name: {
    fontWeight: "bold",
    marginBottom: 4,
  },
  unreadMessagesText: {
    color: "white",
    fontWeight: "bold",
  },
});
