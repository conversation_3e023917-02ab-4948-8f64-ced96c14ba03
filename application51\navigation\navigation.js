import React from 'react'
import {createNativeStackNavigator} from '@react-navigation/native-stack'

import Home from '../screen/Home'
import Login from '../screen/Login'
import Register from '../screen/Register'


const Stack = createNativeStackNavigator();

export default function navigation() {

    // initialRouteName='Login'  الشاشة الافتراضيه

    return <Stack.Navigator initialRouteName='Login'  screenOptions={{
        headerShown:false
    }}>
        <Stack.Screen name='Home' component={Home} />
        <Stack.Screen name='Login' component={Login} />
        <Stack.Screen name='Register' component={Register} />

    </Stack.Navigator>
}
