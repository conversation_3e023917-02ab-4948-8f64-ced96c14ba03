import io from "socket.io-client"; // Importing socket.io-client for real-time communication
import { useEffect, useCallback } from "react";
import Sidebar from "../components/Sidebar/Sidebar";
import { Outlet } from "react-router-dom";
import { useStore } from "../libs/state";
import { getMessages, getUsers } from "../libs/requests";
import axios from "axios";

export default function Home() {
  const {
    addMessage,
    setFriends,
    setSocket,
    setMessages,
    setUser,
    updateFriend,
    setTyping,
    addFriend,
    setCurrentReceiver,
    user,
    accessToken,
    currentReceiver,
    messages,
    friends
  } = useStore();

  // Fetching users and messages from API

  const fetchData = async () => {
    try {
      const [userRes, messagRes] = await Promise.all([
        axios.get("http://localhost:3000/user", {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }),
        axios.get("http://localhost:3000/message", {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        }),
      ]);

      console.log("User data:", userRes.data);
      console.log("Messages data:", messagRes.data);

      setFriends(userRes.data);
      setMessages(messagRes.data);
    } catch (error) {
      console.error("Error fetching data:", error.response?.data || error.message);
    }
  };


  // const fetchData = useCallback(async () => {
  //   try {
  //     const usersRes = await axios.get("http://localhost:3000/user", {
  //       headers: {
  //         Authorization: token,
  //       },
  //     });
  //     const users = usersRes.data;

  //     const messagesRes = await axios.get("http://localhost:3000/message", {
  //       headers: {
  //         Authorization: token,
  //       },
  //     });
  //     const messages = messagesRes.data;

  //     // Update your states here, for example:
  //     // setFriends(users);
  //     // setMessages(messages);

  //   } catch (error) {
  //     console.error("Error fetching data:", error);
  //   }
  // }, [token]);  // Include the necessary dependencie
  useEffect(() => {
    if (!accessToken) {
      console.error("Access token is missing. Cannot connect to the server.");
      return;
    }

    const socket = io('http://localhost:3000', {
      query: { token: accessToken },  // 🔥 تأكد من تمرير التوكن ككائن وليس كسلسلة
      transports: ["websocket"],
    });

    socket.on("connect", () => {
      console.log(`Connected to the server with the id: ${socket.id}`);
      setSocket(socket);
    });

    socket.on("disconnect", () => {
      console.log("Disconnected from the server");
      setSocket(null);
    });

    socket.on("typing",()=> setTyping(true));
    socket.on("stop_typing",()=> setTyping(false));


    return () => {
      socket.disconnect();
    };

  }, [accessToken, setSocket]);


  // Fetch data when component mounts
  useEffect(() => {
    if (accessToken) {
      fetchData();
    }
  }, [accessToken]);

  // Log updated messages for debugging
  useEffect(() => {
    console.log("Messages updated:", messages);
  }, [messages]);

  return (
    <div className="flex h-screen">
      <Sidebar />
      <Outlet />
    </div>
  );
}
