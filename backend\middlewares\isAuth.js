import "dotenv/config";
import jwt from "jsonwebtoken";

// Middleware للتحقق من صحة التوكن
export default async function isAuthenticated(req, res, next) {
  const authHeader = req.headers.authorization.split( " ")[1];

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return res.send({ message: "Authentication invalid" });
  }

  const token = authHeader.split(" ")[1];

  try {
    const data = jwt.verify(socket.handshake.query.token,process.env.JWT_SECRET);
    req.userId = data.userId;
    
  } catch (error) {
    return res.send({ message: "Authentication invalid" });
  }
}

export const isAuth = (socket, next) => {
  const token = socket.handshake?.query?.token;

  if (!token) {
    return (new Error("Authentication invalid"));
  }

  try {
    const data = jwt.verify(socket.handshake.query.token,process.env.JWT_SECRET);
    socket.userId = data.userId;
    next();
  } catch (error) {
    next(new Error("Authentication invalid"));
  }
};

export const isSocketAuthenticated = (socket, next) => {
  const token = socket.handshake?.query?.token;

  if (!token) {
    console.log("Authentication invalid");
    return next(new Error("Authentication invalid")); // هنا الخطأ إذا لم يكن `next` دالة
  }

  try {
    const data = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = data.userId;
    return next(); // تأكد من استدعاء `next()` عند النجاح
  } catch (error) {
    console.error("Error verifying token:", error);
    return next(new Error("Authentication invalid"));
  }
};