import FileSystem from "expo-file-system";

import { API_URL } from "@env";



export function getReceiverMessages(messages, receiverId) {
  if (!messages || !Array.isArray(messages)) return [];
  return messages?.filter(
    (message) =>
      message.senderId === receiverId || message.receiverId === receiverId
  );
}


export async function uploadImageAsync(token ,localUri) {


  const response = await FileSystem.uploadAsync(
    `${API_URL}/user/profile-picture`,
    localUri,
    {
      uploadType: FileSystem.FileSystemUploadType.MULTIPART,
      headers: {
        Authorization: `Bearer ${token}`,
      },
      httpMethod: "PUT",
      fieldName : "profilePicture",
      mimeType : "image/jpeg",
      
    }
  );

  return response;

}