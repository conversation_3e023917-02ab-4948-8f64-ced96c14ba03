import React from 'react'
import {createNativeStackNavigator} from '@react-navigation/native-stack'

import Home from '../screen/Home'
import Login from '../screen/Login'
import Register from '../screen/Register'
import Messages from '../screen/Messages'


const Stack = createNativeStackNavigator();

export default function navigation() {

    // initialRouteName='Login'  الشاشة الافتراضيه

    return <Stack.Navigator initialRouteName='Login'  >

        <Stack.Screen name='Home' component={Home}  />
        <Stack.Screen name='Login' component={Login} />
        <Stack.Screen name='Register' component={Register} />
        <Stack.Screen name='Messages' component={Messages} options={{
            headerShown:true,
            headerStyle : {
                backgroundColor:'red',
                height:100
            },
            headerTitleStyle : {
                color:'white',
                fontSize:30
            },
            headerTintColor:'white'
        }} />

    </Stack.Navigator>
}
