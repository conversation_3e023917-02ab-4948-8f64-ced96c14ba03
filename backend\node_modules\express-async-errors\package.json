{"name": "express-async-errors", "version": "3.1.1", "description": "Async/await error handling support for expressjs", "main": "index.js", "scripts": {"coverage": "nyc mocha test.js", "report": "nyc report --reporter=html", "test": "mocha test.js", "lint": "eslint .", "precommit": "npm run lint"}, "repository": {"type": "git", "url": "git+https://github.com/davidbanham/express-async-errors.git"}, "keywords": ["expressjs", "async/await", "async", "await", "es6"], "author": "<EMAIL>", "license": "ISC", "bugs": {"url": "https://github.com/davidbanham/express-async-errors/issues"}, "homepage": "https://github.com/davidbanham/express-async-errors#readme", "peerDependencies": {"express": "^4.16.2"}, "devDependencies": {"eslint": "^5.6.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-plugin-import": "^2.8.0", "express": "^4.16.2", "husky": "^0.14.3", "mocha": "^4.0.1", "nyc": "^13.0.1", "supertest": "^3.0.0"}}