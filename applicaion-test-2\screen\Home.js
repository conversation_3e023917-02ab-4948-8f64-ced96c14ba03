import React from 'react'
import {createMaterialTopTabNavigator} from '@react-navigation/material-top-tabs'
import {view , Text} from 'react-native'
import { View } from 'react-native'
import Header from '../components/Header'
import { useStore } from '../libs/state';
import { io } from 'socket.io-client';
import { API_URL } from '@env';
import axios from 'axios';
import { useEffect } from 'react';
import  { MaterialCommunityIcons } from 'react-native-vector-icons';

import  Community  from './Community'
import  Chat  from './Chat'
import  Profile  from './Profile'




//  createNativeStackNavigator لجعل التاب على الصفحة
const TopTab = createMaterialTopTabNavigator(); 

export default function Home() {
  const API_URL_1 = process.env.API_URL;
  const {
    setSocket,
    token,
    setFriends,
    setMessage,
    friends,
    user,
    Messages,
    setTyping,
    setUser,
    currentReceiver,
    setCurrentReceiver,
  } = useStore();

  useEffect(() => {
    // الاتصال بالخادم باستخدام Socket.IO
    const socket = io(API_URL_1, {
      query: { token },
    });

    setSocket(socket);

    socket.on('connect', () => {
      console.log('Connected to the server with id:', socket.id);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from the server');
    });

    socket.on('user_updated', (userUpdated) => {
      if (user._id === userUpdated._id) {
        setUser(userUpdated);
      } else {
        const index = friends.findIndex((friend) => friend._id === userUpdated._id);
        if (index !== -1) {
          const updatedFriends = [...friends];
          updatedFriends[index] = userUpdated;
          setFriends(updatedFriends);

          if (currentReceiver?._id === userUpdated._id) {
            setCurrentReceiver(userUpdated);
          }
        }
      }
    });

    socket.on('typing', () => setTyping(true));
    socket.on('stop_typing', () => setTyping(false));

    socket.on('user_created_successfully', (userCreated) => {
      if (user._id !== userCreated._id) {
        setFriends((prevFriends) => [...prevFriends, userCreated]);
      }
    });

    socket.on('reciver_message', (newMessage) => {
      setMessage((prevMessages) => [...prevMessages, newMessage]);
    });

    // جلب البيانات عند تحميل الصفحة
    const fetchData = async () => {
      try {
        const usersRes = await axios.get(`${API_URL_1}/user`, {
          headers: {
            Authorization: token,
          },
        });
        const users = usersRes.data;

        const messagesRes = await axios.get(`${API_URL_1}/message`, {
          headers: {
            Authorization: token,
          },
        });
        const messages = messagesRes.data;

        setFriends(users);
        setMessage(messages);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();

   
  }, []);
  return (
  <>
   <Header />
   <TopTab.Navigator initialRouteName='Chat' options={{headerShown:false}}>
    <TopTab.Screen name="Chat" component={Chat} />
    <TopTab.Screen name="Community" component={Community} options={{
      tobBarLabel : 'Community',
      tabBarIcon : ({color}) => <MaterialCommunityIcons name="account-group" size={24} color={color} />
    }} />
    <TopTab.Screen name="Profile" component={Profile} />
  </TopTab.Navigator>

 </>
  )
}
