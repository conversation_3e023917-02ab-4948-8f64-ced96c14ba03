import React, { useState } from "react";
import {
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import {
  Box,
  Text,
  Button,
  Link,
  Input,
  FormControl,
  VStack,
  Heading,
  Toast,
  HStack,
} from "native-base";
import * as Yup from "yup";
import { useFormik } from "formik";
import axios from "../libs/Api";
import { useStore } from "../libs/state";
import { useNavigation } from "@react-navigation/native";


export default function Login() {
  const [isLoading, setIsLoading] = useState(false);
  const navigation = useNavigation();
  const { setUser, setAccessToken } = useStore();

  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      password: Yup.string()
        .min(6, "Password must be at least 6 characters")
        .required("Password is required"),
    }),
  });

  async function onsubmit() {
    setIsLoading(true);
    try {
      const errors = Object.values(formik.errors);
      if (errors.length > 0) {
        Toast.show({
          title: errors.join("\n"),
          status: "error",
          placement: "top",
          
        });
      } else {
        const response = await axios.post("/user/login", {
          email: formik.values.email,
          password: formik.values.password,
        });
        Toast.show({
          title: response.data.message || "Success",
          status: response.data.errors ? "error" : "success",
          placement: "top",
        });

       
        setUser(response.data.user);
        setAccessToken(response.data.token);
        console.error("response.data.user", response.data.user);

        navigation.navigate("Home");
      }
    } catch (error) {
      Toast.show({
        title: error.message || "Something went wrong!",
        status: "error",
        placement: "top",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <Box safeArea w="90%" maxW={290}>
        <Image
          source={require("../assets/logo-hsoub.png")}
        />
        <Heading size="lg" fontWeight={600} color={"coolGray.800"}>
          Welcome
        </Heading>
        <Heading
          size="xs"
          mt="1"
          fontWeight="medium"
          color={"coolGray.600"}
        >
          Login to continue!
        </Heading>
        <VStack space={3} mt="5" w="100%">
          <FormControl >
            <FormControl.Label>Email Address</FormControl.Label>
            <Input
              placeholder="Enter your email address"
              onChangeText={formik.handleChange("email")}
              value={formik.values.email}
            />
          
          </FormControl>

          <FormControl >
            <FormControl.Label>Password</FormControl.Label>
            <Input
              placeholder="Enter your password"
              value={formik.values.password}
              onChangeText={formik.handleChange("password")}
              secureTextEntry={true}
            />
           
          </FormControl>
          <Button
            mt="2"
            colorScheme="indigo"
            onPress={onsubmit}
            isLoading={isLoading}
          >
            Login
          </Button>
          <HStack mt="6" justifyContent="center">
            <Text fontSize="sm" color="coolGray.600">
              I'm a new user.
            </Text>
            <Link
              _text={{
                color: "indigo.500",
                fontWeight: "medium",
                fontSize: "sm",
              }}
              onPress={() =>  navigation.navigate("Register")}
            >
              Register
            </Link>
          </HStack>
        </VStack>
      </Box>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  logo: {
    width: 400,
    height: 200,
    alignSelf: "center",
  },
  container: {
    flex: 1,
    backgroundColor: "#fff",
    alignItems: "center",
    justifyContent: "center",
  },
});
