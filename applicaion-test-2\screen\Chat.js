import React from 'react'
import { createNativeStackNavigator } from '@react-navigation/native-stack'
import {view , Text , FlatList} from 'react-native'
import { View } from 'react-native'
import ChatItem from '../components/ChatItem'
import { useStore } from '../libs/state'


export default function Chat() {
  const {friends} = useStore();

  return (
    <View>
      <FlatList 
      data={friends}
       keyExtractor={(item ,i) => i.toString()}
       renderItem={({item}) => <ChatItem 
       _id={item.id}
        name={item.username}
        profilePicture={item.profilePicture} 
        />} 
        />
     </View>
  )
}